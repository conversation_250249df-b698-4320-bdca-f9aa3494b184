'use client';

import type { ReturnRequestWithItems } from '@/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import { Volume2, VolumeX } from 'lucide-react';
import { useState } from 'react';
import { updateReturnRequest } from '../actions';
import { BarcodeInput } from './components/barcode-input';
import { ExchangeStepControls } from './components/exchange-step-controls';
import { ItemsList } from './components/items-list';
import { ProgressIndicator } from './components/progress-indicator';
import { ReturnLabelNumberInput } from './components/return-label-number-input';
import { ScanResult } from './components/scan-result';
import { SubmitButton } from './components/submit-button';
import { useScanningLogic } from './hooks/use-scanning-logic';

interface BarcodeScannerProps {
  request: ReturnRequestWithItems;
  skipReturnPackageScan?: boolean;
  dictionary: Dictionary;
  onItemScanned?: (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => void;
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
  onReturnLabelNumberSubmit?: (returnLabelNumber: string) => Promise<void>;
}

export function BarcodeScanner({
  request,
  skipReturnPackageScan = false,
  dictionary,
  onItemScanned,
  onSubmit,
  onReturnLabelNumberSubmit,
}: BarcodeScannerProps) {
  // Exchange step state - set initial step based on processed status
  const getInitialExchangeStep = () => {
    if (request.exchangeType === 'exchange' && request.processed) {
      if (request.processed === 'pending') {
        return 'outgoing';
      }
      if (request.processed === 'exchange_shipped') {
        return 'incoming';
      }
    }
    return 'outgoing';
  };

  const [exchangeStep, setExchangeStep] = useState<'outgoing' | 'incoming'>(
    getInitialExchangeStep()
  );
  const [audioEnabled, setAudioEnabled] = useState(true);

  // Return label number state
  const [returnLabelNumberEntered, setReturnLabelNumberEntered] = useState(
    // if return label option is envelope, or return label number is already entered
    request.returnLabelOption === 'envelope' || !!request.returnLabelNumber
  );
  const [currentReturnLabelNumber, setCurrentReturnLabelNumber] = useState(
    request.returnLabelNumber || ''
  );

  // Use the scanning logic hook
  const {
    productChecklist,
    scanResult,
    errorMessage,
    returnPackageVerified,
    scanningStep,
    scannedCount,
    totalCount,
    allScanned,
    setAudioEnabled: setScanningAudioEnabled,
    handleReturnPackageVerification,
    handleCheckProduct,
    handleExchangeScanning,
    handleMarkAsScanned,
    resetChecklist,
  } = useScanningLogic({
    returnItems: request.returnItems ?? [],
    returnNumber: request.returnNumber,
    returnLabelNumber: request.returnLabelNumber ?? null,
    initialScanningStep: skipReturnPackageScan ? 'items' : 'package',
    onItemScanned,
  });

  // Sync audio enabled state
  const handleAudioToggle = () => {
    setAudioEnabled(!audioEnabled);
    setScanningAudioEnabled(!audioEnabled);
  };

  // Handle scanning based on type and step
  const handleScan = (value: string) => {
    if (request.exchangeType === 'return' && scanningStep === 'package') {
      handleReturnPackageVerification(value);
    } else if (request.exchangeType === 'return' && scanningStep === 'items') {
      handleCheckProduct(value);
    } else if (
      request.exchangeType === 'exchange' &&
      exchangeStep === 'outgoing'
    ) {
      handleExchangeScanning(value, 'outgoing');
    } else if (
      request.exchangeType === 'exchange' &&
      exchangeStep === 'incoming'
    ) {
      handleExchangeScanning(value, 'incoming');
    } else {
      // Default behavior for null exchange type (legacy returns)
      handleCheckProduct(value);
    }
  };

  // Handle exchange step change
  const handleExchangeStepChange = (step: 'outgoing' | 'incoming') => {
    setExchangeStep(step);
    resetChecklist();
  };

  // Helper functions for UI content
  const getCardTitle = () => {
    if (request.exchangeType === 'exchange') {
      return exchangeStep === 'outgoing'
        ? dictionary.admin.scanner.barcode_scanner.outgoing_items
        : dictionary.admin.scanner.barcode_scanner.incoming_items;
    }
    if (request.exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.package_verification;
    }
    return dictionary.admin.scanner.barcode_scanner.item_verification;
  };

  const getCardDescription = () => {
    if (request.exchangeType === 'exchange') {
      if (exchangeStep === 'outgoing') {
        return dictionary.admin.scanner.barcode_scanner.scan_outgoing
          .replace('{scannedCount}', scannedCount.toString())
          .replace('{totalCount}', totalCount.toString());
      }

      return dictionary.admin.scanner.barcode_scanner.scan_incoming
        .replace('{scannedCount}', scannedCount.toString())
        .replace('{totalCount}', totalCount.toString());
    }
    if (request.exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.scan_package.replace(
        '{returnNumber}',
        `${request.returnNumber} / ${request.returnLabelNumber}`
      );
    }
    return dictionary.admin.scanner.barcode_scanner.scan_items
      .replace('{scannedCount}', scannedCount.toString())
      .replace('{totalCount}', totalCount.toString());
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{getCardTitle()}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleAudioToggle}
            className="flex items-center gap-2"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
            {audioEnabled
              ? dictionary.admin.scanner.barcode_scanner.audio_on
              : dictionary.admin.scanner.barcode_scanner.audio_off}
          </Button>
        </CardTitle>
        <CardDescription>{getCardDescription()}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Exchange step controls */}
        {request.exchangeType === 'exchange' && (
          <ExchangeStepControls
            exchangeStep={exchangeStep}
            processed={request.processed}
            onStepChange={handleExchangeStepChange}
          />
        )}

        {/* Progress indicator */}
        <ProgressIndicator
          exchangeType={request.exchangeType}
          scanningStep={scanningStep}
          returnPackageVerified={returnPackageVerified}
          scannedCount={scannedCount}
          totalCount={totalCount}
        />

        {returnLabelNumberEntered ? (
          <>
            {/* Scan result and error messages */}
            <ScanResult scanResult={scanResult} errorMessage={errorMessage} />

            {/* Barcode Scanner Input */}
            <BarcodeInput
              exchangeType={request.exchangeType}
              exchangeStep={exchangeStep}
              scanningStep={scanningStep}
              returnNumber={request.returnNumber}
              returnLabelNumber={request.returnLabelNumber ?? undefined}
              onScan={handleScan}
              dictionary={dictionary}
            />

            {/* Items list */}
            <ItemsList
              items={productChecklist}
              exchangeType={request.exchangeType}
              exchangeStep={exchangeStep}
              scanningStep={scanningStep}
              onMarkAsScanned={handleMarkAsScanned}
            />

            {/* Submit button */}
            <SubmitButton
              exchangeType={request.exchangeType}
              exchangeStep={exchangeStep}
              scanningStep={scanningStep}
              returnPackageVerified={returnPackageVerified}
              allScanned={allScanned}
              productChecklist={productChecklist}
              onSubmit={(scannedItems) => {
                onSubmit?.(scannedItems);

                if (
                  request.exchangeType === 'exchange' &&
                  exchangeStep === 'outgoing'
                ) {
                  handleExchangeStepChange('incoming');
                  return;
                }
              }}
              dictionary={dictionary}
            />
          </>
        ) : (
          <ReturnLabelNumberInput
            returnNumber={request.returnNumber}
            currentValue={currentReturnLabelNumber}
            dictionary={dictionary}
            onSubmit={async (returnLabelNumber) => {
              await onReturnLabelNumberSubmit?.(returnLabelNumber);

              if (request.status !== 'approved') {
                await updateReturnRequest(request.id, {
                  status: 'approved',
                });
              }

              setCurrentReturnLabelNumber(returnLabelNumber);
              setReturnLabelNumberEntered(true);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
}
