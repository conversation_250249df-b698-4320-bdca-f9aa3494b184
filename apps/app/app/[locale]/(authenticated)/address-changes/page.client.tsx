'use client';

import type { Dictionary } from '@repo/internationalization';
import { columns } from './components/address-change-column';
import type { AddressChangeRow } from './components/address-change-column';
import { AddressChangeTable } from './components/address-change-table';

export function AddressChangeRequestsPageClient({
  addressChangeRequests,
  dictionary,
}: {
  addressChangeRequests: AddressChangeRow[];
  dictionary: Dictionary;
}) {
  return (
    <AddressChangeTable
      dictionary={dictionary}
      columns={columns(dictionary)}
      initialData={addressChangeRequests}
    />
  );
}
