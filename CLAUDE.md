# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

Senders Return is a monorepo built with Turborepo containing:
- **Apps**: Next.js applications (api, app, portal, email-worker)
- **Packages**: Shared libraries and utilities

### Key Applications
- `apps/api`: Backend API server (port 3002)
- `apps/app`: Main admin application (port 3000)  
- `apps/portal`: Customer-facing portal (port 3003)
- `apps/email-worker`: Background email processing

### Core Packages
- `@repo/database`: Prisma database client and schema
- `@repo/auth`: Better-auth authentication system
- `@repo/design-system`: Shared UI components (shadcn/ui + Tailwind)
- `@repo/email`: Email templates and sending logic
- `@repo/email-queue`: BullMQ email queue with Redis
- `@repo/internationalization`: i18n support (en/ja)

## Development Commands

### Core Development
```bash
pnpm dev              # Start all dev servers (excludes email-worker)
pnpm portal           # Start only portal app
pnpm build            # Build all applications
pnpm lint             # Lint with Biome
pnpm format           # Format with Biome
pnpm test             # Run all tests
```

### Database Management
```bash
pnpm migrate          # Run database migrations
pnpm seed             # Seed database with test data
pnpm reset            # Reset database and run migrations
pnpm prisma           # Format and generate Prisma client
```

### Application-Specific
```bash
# Run specific apps with turbo filter
turbo dev --filter=api
turbo dev --filter=app
turbo dev --filter=portal

# Build specific apps
turbo build --filter=api
```

## Environment Setup

Each app has its own `.env` file (copy from `.env.example`):
- `apps/api/.env` - API server configuration
- `apps/app/.env` - Admin app configuration  
- `apps/portal/.env` - Portal configuration
- `apps/email-worker/.env` - Email worker configuration

Required environment variables typically include:
- Database URL
- Redis URL (for email queue)
- Authentication secrets
- Email service credentials
- Shopify API credentials

## Code Style & Quality

- **Linting**: Biome (`pnpm lint`)
- **Formatting**: Biome (`pnpm format`)
- **Testing**: Vitest with React Testing Library
- **TypeScript**: Strict mode enabled

## Key Patterns

### Database Access
Use `@repo/database` package for all database operations:
```typescript
import { db } from '@repo/database'
const users = await db.user.findMany()
```

### Authentication
Use `@repo/auth` for auth operations:
```typescript
import { auth } from '@repo/auth'
const session = await auth.api.getSession({ headers })
```

### Email Queue
Use `@repo/email-queue` for background email processing:
```typescript
import { emailQueue } from '@repo/email-queue'
await emailQueue.add('send-welcome-email', { userId })
```

### UI Components
Import from `@repo/design-system` for consistent UI:
```typescript
import { Button, Card, Input } from '@repo/design-system'
```

## Testing

Run tests for specific applications:
```bash
# Run api tests
turbo test --filter=api

# Run app tests  
turbo test --filter=app

# Run portal tests
turbo test --filter=portal
```

Test files are located in `__tests__` directories within each app.

## Deployment

Applications are configured for Vercel deployment. Each app has:
- `vercel.json` - Vercel configuration
- `next.config.ts` - Next.js configuration
- Environment-specific build settings

## Common Development Tasks

### Adding New Database Models
1. Update `packages/database/prisma/schema.prisma`
2. Run `pnpm migrate` to create migration
3. Run `pnpm prisma` to generate client

### Creating New API Routes
Add files to `apps/api/app/api/` following Next.js 13+ app router patterns.

### Adding UI Components
Create components in `packages/design-system/components/ui/` and export from `packages/design-system/index.tsx`.

### Internationalization
Use `@repo/internationalization` package. Translations are in `dictionaries/en.json` and `dictionaries/ja.json`.

## Troubleshooting

- **Database issues**: Run `pnpm reset` to reset database
- **Build issues**: Run `pnpm clean` to clear node_modules
- **Type errors**: Run `pnpm prisma` to regenerate Prisma client
- **Cache issues**: Use `turbo build --force` to bypass cache