// Define types for the return flow
import type { GetOrdersQuery } from '@/types/admin.generated';
import type { Dictionary } from '@repo/internationalization';

export type ReturnReason =
  | 'wrong_size_model'
  | 'defect'
  | 'different_description'
  | 'wrong_product'
  | 'different_image'
  | 'no_longer_needed'
  | 'customer_damage';

export type DefectType = 'broken' | 'scratched' | 'dirt' | 'other';

export type RefundMethod =
  | 'original_payment'
  | 'store_credit'
  | 'bank_transfer';
export type ExchangeMethod = 'pickup' | 'ship';

// Define the flow state type
export type FlowState = {
  step: number;
  returnReason?: ReturnReason;
  defectType?: DefectType;
  defectDetails?: string;
  defectPhotoUrl?: string;
  defectPhotos?: string[]; // Multiple photos for defects
  hasAccessories?: boolean;
  exchangeType?: 'exchange' | 'return';
  refundMethod?: RefundMethod;
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    routingNumber: string;
  };
  exchangeMethod?: ExchangeMethod;
  returnLabelOption?: 'print' | 'envelope'; // Option for printing label or getting return envelope
  pickupDetails?: {
    location: string;
    date: string;
    time: string;
  };
  shippingAddress?: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  newSizes?: Record<string, string>;
  returnNumber?: string;
  isRejected?: boolean; // Flag for rejection flow
  isProcessing?: boolean; // Flag for processing state during rejection check
  isCheckingRejectedReturns?: boolean; // Flag for loading state while checking for rejected returns (legacy)
  isCheckingReturns?: boolean; // Flag for loading state while checking for all types of returns
  processingDuration?: number; // Random duration for processing
  refundFee?: number; // Penalty fee for wrong size/model
  orderDate?: Date; // Date when the order was placed
  isWithinReturnPeriod?: boolean; // Flag to check if return is within allowed period
  isSpecialApproval?: boolean; // Flag for returns between 10-30 days
  isExpired?: boolean; // Flag for returns older than 30 days
  hasDeliveryDate?: boolean; // Flag to indicate if the order has a delivery date
  lineItemsProcessed?: string[]; // Array of line item IDs that have been processed
  termsChecked?: boolean; // Flag to indicate if terms checkbox is checked
  policyChecked?: boolean; // Flag to indicate if policy checkbox is checked
  existingReturnStatus?: string | null; // Status of existing return (pending, approved, rejected, etc.)
  existingReturnLabelOption?: 'print' | 'envelope' | null; // Label option for existing return
  existingReturnNumber?: string | null; // Return number for existing return
  autoApprovalDays: number; // Number of days for auto approval
  isBlacklisted?: boolean; // Flag to indicate if the user is blacklisted
};

export interface ReturnFlowStepperProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  onComplete: () => void;
  onCancel: () => void;
  autoApprovalDays: number;
}

export interface StepProps {
  orderName: string;
  flowState: FlowState;
  updateFlowState: (updates: Partial<FlowState>) => void;
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  dictionary: Dictionary;
  totalAmount: number;
}
