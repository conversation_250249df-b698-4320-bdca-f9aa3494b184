'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { formatDate } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import type { ColumnDef } from '@tanstack/react-table';

// Define the ReturnRequest type based on what we're selecting in the action
export type ReturnRequestRow = {
  id: string;
  orderName: string;
  email: string;
  returnNumber: string;
  returnReason: string;
  exchangeType: string | null;
  status: string;
  processed: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    returnItems: number;
    defectPhotos: number;
  };
};

export const columns = (
  dictionary: Dictionary
): ColumnDef<ReturnRequestRow>[] => [
  {
    accessorKey: 'returnNumber',
    header: dictionary.admin.table.return_number,
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('returnNumber')}</div>
    ),
  },
  {
    accessorKey: 'orderName',
    header: dictionary.admin.table.order,
  },
  {
    accessorKey: 'email',
    header: dictionary.admin.table.customer_email,
  },
  {
    accessorKey: 'returnReason',
    header: dictionary.admin.table.reason,
    cell: ({ row }) => {
      const reason = row.getValue('returnReason') as string;
      const text = dictionary.return.reason[reason];

      return <div className="capitalize">{text}</div>;
    },
  },
  {
    accessorKey: 'status',
    header: dictionary.admin.common.status,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      let variant: 'default' | 'success' | 'destructive' | 'secondary' =
        'secondary';
      let text = dictionary.admin.return_requests.status_pending;

      if (status === 'completed') {
        variant = 'default';
        text = dictionary.admin.return_requests.status_completed;
      } else if (status === 'approved') {
        variant = 'success';
        text = dictionary.admin.return_requests.status_approved;
      } else if (status === 'rejected') {
        variant = 'destructive';
        text = dictionary.admin.return_requests.status_rejected;
      }

      return <Badge variant={variant}>{text}</Badge>;
    },
  },
  {
    accessorKey: 'processed',
    header: dictionary.admin.return_requests.processed,
    cell: ({ row }) => {
      const processed = row.getValue('processed') as string;

      let variant: 'default' | 'success' | 'destructive' | 'secondary' =
        'secondary';
      let text = dictionary.admin.return_requests.status_pending;

      if (processed === 'exchange_shipped') {
        text =
          dictionary.admin.return_requests.status_waiting_for_customer_return;
        variant = 'default';
      } else if (processed === 'completed') {
        text = dictionary.admin.return_requests.status_completed;
        variant = 'success';
      }

      return <Badge variant={variant}>{text}</Badge>;
    },
  },
  {
    accessorKey: '_count.returnItems',
    header: dictionary.admin.table.items,
    cell: ({ row }) => {
      const count = row.original._count.returnItems;
      return <div className="text-center">{count}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: dictionary.admin.table.created,
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
];
