import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';

type SendersConfirmationEmailProps = {
  readonly shareId?: string;
  readonly title?: string;
  readonly message?: string;
  readonly fileCount?: number;
  readonly expiresAt?: string;
  readonly isPrivate?: boolean;
  readonly dictionary: EmailDictionary;
};

export const SendersConfirmationEmail = ({
  shareId,
  title,
  message,
  fileCount,
  expiresAt,
  isPrivate,
  dictionary,
}: SendersConfirmationEmailProps) => {
  const t = dictionary.senders_confirmation;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {title
            ? interpolateEmailTemplate(t.preview, { title })
            : t.preview_no_title}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://www.senders.jp/logo.png"
              alt="Senders Logo"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {title
                    ? interpolateEmailTemplate(t.title, { title })
                    : t.title_default}
                </Text>
                {message && (
                  <>
                    <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                      {t.message_label}
                    </Text>
                    <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">
                      {message}
                    </Text>
                  </>
                )}
                <Hr className="my-4" />
                <Text className="m-0 mb-2 text-zinc-700">
                  <strong>{t.file_count}</strong> {fileCount ?? 'N/A'}
                </Text>
                {expiresAt && (
                  <Text className="m-0 mb-2 text-zinc-700">
                    <strong>{t.expires}</strong> {expiresAt}
                  </Text>
                )}
                <Text className="m-0 mb-2 text-zinc-700">
                  <strong>{t.privacy}</strong>{' '}
                  {isPrivate ? t.private : t.public}
                </Text>
                {shareId && (
                  <Text className="m-0 mt-4 text-blue-600 underline">
                    <a href={`https://www.senders.jp/share/${shareId}`}>
                      {t.view_button}
                    </a>
                  </Text>
                )}
                <Hr className="my-4" />
                <Text className="m-0 text-sm text-zinc-500">
                  {t.automatic_notice}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleSendersConfirmationEmail = () => {
  const mockDictionary = {
    senders_confirmation: {
      preview: '{title} - Your files have been sent successfully',
      preview_no_title: 'Your files have been sent successfully',
      title: '{title}',
      title_default: 'Your files have been sent successfully',
      message_label: 'Message:',
      share_id: 'Share ID:',
      file_count: 'Files sent:',
      expires: 'Expires:',
      privacy: 'Privacy:',
      private: 'Private',
      public: 'Public',
      view_button: 'View Files',
      automatic_notice:
        'This email was sent automatically. If you have any questions, please contact support.',
    },
  } as EmailDictionary;

  return (
    <SendersConfirmationEmail
      shareId="SHARE-12345"
      title="Project Files Sent"
      message="You have successfully sent the files for the project."
      fileCount={3}
      expiresAt="2024-07-01"
      isPrivate={true}
      dictionary={mockDictionary}
    />
  );
};

export default ExampleSendersConfirmationEmail;
