import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * GET /api/cms
 * Retrieves all CMS content or specific content by key
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');
    const locale = searchParams.get('locale') || 'en';

    if (key) {
      // Get specific content by key
      const content = await prisma.cmsContent.findUnique({
        where: {
          key,
          isPublished: true,
        },
      });

      if (!content) {
        return NextResponse.json(
          { error: 'Content not found' },
          { status: 404 }
        );
      }

      // Extract content for the requested locale or fallback to English
      const localizedContent = content.content as Record<string, string>;
      const contentText =
        localizedContent[locale] || localizedContent['en'] || '';

      return NextResponse.json({
        key: content.key,
        title: content.title,
        content: contentText,
      });
    }

    // Get all content
    const contents = await prisma.cmsContent.findMany({
      where: {
        isPublished: true,
      },
      select: {
        key: true,
        title: true,
        content: true,
      },
    });

    // Format the response to include localized content
    const formattedContents = contents.map((item) => {
      const localizedContent = item.content as Record<string, string>;
      return {
        key: item.key,
        title: item.title,
        content: localizedContent[locale] || localizedContent['en'] || '',
      };
    });

    return NextResponse.json(formattedContents);
  } catch (error) {
    console.error('Error fetching CMS content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch content' },
      { status: 500 }
    );
  }
}
