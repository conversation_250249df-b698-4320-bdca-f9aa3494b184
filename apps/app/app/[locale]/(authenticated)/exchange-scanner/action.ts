'use server';

import prisma from '@repo/database/prisma-client';
import { log } from '@repo/observability/log';

export async function findReturnByTracking(trackingOrLabelNumber: string) {
  log.info(
    `Searching for return request with tracking or label number: ${trackingOrLabelNumber}`
  );

  try {
    // Find return request by tracking number OR label number
    const returnRequest = await prisma.returnRequest.findFirst({
      where: {
        OR: [
          { trackingNumber: trackingOrLabelNumber },
          { returnLabelNumber: trackingOrLabelNumber },
        ],
      },
      select: {
        id: true,
        status: true,
        returnNumber: true,
        returnLabelNumber: true,
        returnLabelOption: true,
        processed: true,
        exchangeType: true,
        orderName: true,
        email: true,
        returnItems: {
          select: {
            id: true,
            title: true,
            sku: true,
            barcode: true,
            quantity: true,
          },
        },
      },
    });

    return returnRequest;
  } catch (error) {
    log.error('Error searching for return request:', { error });
    return null;
  }
}
