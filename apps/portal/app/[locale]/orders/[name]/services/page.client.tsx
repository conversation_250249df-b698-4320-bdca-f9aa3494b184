'use client';

import type { GetOrderBasicInfoQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle2,
  ExternalLink,
  Home,
  Package,
  Receipt,
  RefreshCw,
  X,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import Link from 'next/link';
import { useEffect, useState } from 'react';

interface ServiceSelectionClientProps {
  order: NonNullable<GetOrderBasicInfoQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  success?: string;
}

export default function ServiceSelectionClient({
  order,
  dictionary,
  success,
}: ServiceSelectionClientProps) {
  const router = useRouter();
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertTitle, setAlertTitle] = useState('');

  // Handle success messages
  useEffect(() => {
    if (success) {
      setShowAlert(true);

      // Set appropriate success message based on the success parameter
      switch (success) {
        case 'address_updated': {
          setAlertTitle(
            dictionary.alert?.address_updated_title || 'Address Updated'
          );
          setAlertMessage(
            dictionary.alert?.address_updated_message ||
              'Your shipping address has been successfully updated.'
          );
          break;
        }
        case 'receipt_downloaded': {
          setAlertTitle(
            dictionary.alert?.receipt_downloaded_title || 'Receipt Downloaded'
          );
          setAlertMessage(
            dictionary.alert?.receipt_downloaded_message ||
              'Your receipt has been successfully downloaded.'
          );
          break;
        }
        case 'return_processed': {
          setAlertTitle(
            dictionary.alert?.return_processed_title || 'Return Processed'
          );
          setAlertMessage(
            dictionary.alert?.return_processed_message ||
              'Your return request has been successfully processed.'
          );
          break;
        }
        case 'delivery_confirmed': {
          setAlertTitle(
            dictionary.alert?.delivery_confirmed_title || 'Delivery Confirmed'
          );
          setAlertMessage(
            dictionary.alert?.delivery_confirmed_message ||
              'Your delivery date has been confirmed.'
          );
          break;
        }
        default: {
          setAlertTitle(dictionary.alert?.success_title || 'Success');
          setAlertMessage(
            dictionary.alert?.success_message ||
              'Your request has been successfully processed.'
          );
        }
      }

      // Alert will stay visible until user closes it or navigates away
    }
  }, [success, dictionary]);

  const services = [
    {
      id: 'returns',
      title: dictionary.return.returns_exchanges,
      description:
        dictionary.return.returns_exchanges_description ||
        'Process a return or exchange for your order',
      icon: <RefreshCw className="h-4 w-4" />,
      onClick: () => {
        // Navigate to the returns page
        router.push(
          `/orders/${order.name.replace('#', '')}/returns?email=${encodeURIComponent(
            order.email || ''
          )}`
        );
      },
    },
    {
      id: 'receipt',
      title: dictionary.return.download_receipt,
      description:
        dictionary.return.download_receipt_description ||
        'Download a receipt for your order',
      icon: <Receipt className="h-4 w-4" />,
      onClick: () => {
        router.push(
          `/orders/${order.name.replace('#', '')}/receipt?email=${encodeURIComponent(
            order.email || ''
          )}`
        );
      },
    },
    {
      id: 'address',
      title: dictionary.return.change_address,
      description:
        dictionary.return.change_address_description ||
        'Update your shipping address',
      icon: <Home className="h-4 w-4" />,
      onClick: () => {
        router.push(
          `/orders/${order.name.replace('#', '')}/address?email=${encodeURIComponent(
            order.email || ''
          )}`
        );
      },
    },
    {
      id: 'delivery',
      title: dictionary.return.delivery_confirmation,
      description:
        dictionary.return.delivery_confirmation_description ||
        'Check your delivery status',
      icon: <Package className="h-4 w-4" />,
      onClick: () => {
        router.push(
          `/orders/${order.name.replace('#', '')}/delivery?email=${encodeURIComponent(
            order.email || ''
          )}`
        );
      },
    },
  ];

  const handleBack = () => {
    // Navigate back to the home page
    router.push('/');
  };

  return (
    <div className="mx-auto max-w-2xl">
      {showAlert && (
        <div className="mb-6">
          <Alert className="relative" variant="success">
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>{alertTitle}</AlertTitle>
            <AlertDescription>
              {alertMessage}
              {success === 'address_updated' && (
                <div className="mt-3">
                  <Link href={order.statusPageUrl} target="_blank">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <ExternalLink className="h-4 w-4" />
                      {dictionary.button.view_order || 'View Order'}
                    </Button>
                  </Link>
                </div>
              )}
            </AlertDescription>
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 h-6 w-6 p-0"
              onClick={() => setShowAlert(false)}
              aria-label="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        </div>
      )}

      <div className="mb-8 text-center">
        <h2 className="font-semibold text-2xl">
          {dictionary.return.inquiry_type}
        </h2>
        <p className="mt-2 text-muted-foreground">
          {dictionary.return.select_service || 'Please select a service'}
        </p>
      </div>

      <div className="space-y-4">
        {services.map((service) => (
          <Card
            key={service.id}
            className="cursor-pointer overflow-hidden shadow-xs transition-shadow hover:shadow-md"
            onClick={service.onClick}
          >
            <CardContent className="flex items-center px-4">
              <div className="mr-4 flex-shrink-0 rounded-full bg-muted p-3">
                {service.icon}
              </div>
              <div className="flex-grow">
                <h3 className="font-medium">{service.title}</h3>
                <p className="text-muted-foreground text-sm">
                  {service.description}
                </p>
              </div>
              <div className="ml-2 flex-shrink-0">
                <ArrowRight className="h-5 w-5 text-muted-foreground-400" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-8 text-center">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center gap-2 "
        >
          <ArrowLeft className="h-4 w-4" />
          {dictionary.button.back}
        </Button>
      </div>
    </div>
  );
}
