import { render } from '@react-email/components';
import type { ReactElement } from 'react';
import {} from './i18n';
import { sendgrid } from './index';
import { keys } from './keys';
import { generateReturnLabelPDF } from './utils/pdf-generator';

const env = keys();

export interface EmailAttachment {
  content: string; // base64 encoded content
  filename: string;
  type: string; // MIME type
  disposition: 'attachment' | 'inline';
}

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: ReactElement;
  from?: string;
  attachments?: EmailAttachment[];
}

export async function sendEmail({
  to,
  subject,
  template,
  from = env.SENDGRID_FROM,
  attachments,
}: SendEmailOptions) {
  try {
    const html = await render(template);

    const msg = {
      to: Array.isArray(to) ? to : [to],
      from,
      subject,
      html,
      ...(attachments && attachments.length > 0 && { attachments }),
    };

    const result = await sendgrid.send(msg);
    return { success: true, result };
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

// Specific email sending functions for each template type
export async function sendReturnRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    // New optional fields for PDF payload
    returnDate?: string;
    items?: Array<{ itemName: string; quantity: number; sku?: string }>;
  },
  locale = 'ja'
) {
  const { ReturnRequestAutoApprovedEmail } = await import(
    './templates/return-request-auto-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        returnDate: returnLabelData.returnDate,
        items: returnLabelData.items,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'return_request_auto_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ReturnRequestAutoApprovedEmail({
      returnNumber,
      refundDays,
      dictionary,
    }),
    attachments,
  });
}

export async function sendReturnRequestManualReceivedEmail(
  to: string,
  returnNumber: string,
  reviewDays?: string,
  locale = 'ja'
) {
  const { ReturnRequestManualReceivedEmail } = await import(
    './templates/return-request-manual-received'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'return_request_manual_received',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ReturnRequestManualReceivedEmail({
      returnNumber,
      reviewDays,
      dictionary,
    }),
  });
}

export async function sendReturnRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnInstructions?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    // New optional fields for PDF payload
    returnDate?: string;
    items?: Array<{ itemName: string; quantity: number; sku?: string }>;
  },
  locale = 'ja'
) {
  const { ReturnRequestManualApprovedEmail } = await import(
    './templates/return-request-manual-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        returnDate: returnLabelData.returnDate,
        items: returnLabelData.items,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'return_request_manual_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ReturnRequestManualApprovedEmail({
      returnNumber,
      refundDays,
      returnInstructions,
      dictionary,
    }),
    attachments,
  });
}

export async function sendExchangeRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    // New optional fields for PDF payload
    returnDate?: string;
    items?: Array<{ itemName: string; quantity: number; sku?: string }>;
  },
  locale = 'ja'
) {
  const { ExchangeRequestAutoApprovedEmail } = await import(
    './templates/exchange-request-auto-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        returnDate: returnLabelData.returnDate,
        items: returnLabelData.items,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'exchange_request_auto_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeRequestAutoApprovedEmail({
      returnNumber,
      dictionary,
    }),
    attachments,
  });
}

export async function sendExchangeItemShippedEmail(
  to: string,
  returnNumber: string,
  trackingNumber: string,
  locale = 'ja'
) {
  const { ExchangeItemShippedEmail } = await import(
    './templates/exchange-item-shipped'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'exchange_item_shipped',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeItemShippedEmail({
      returnNumber,
      trackingNumber,
      dictionary,
    }),
  });
}

export async function sendExchangeItemReceivedEmail(
  to: string,
  returnNumber: string,
  locale = 'ja'
) {
  const { ExchangeItemReceivedEmail } = await import(
    './templates/exchange-item-received'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'exchange_item_received',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeItemReceivedEmail({ returnNumber, dictionary }),
  });
}

export async function sendExchangeRequestManualReceivedEmail(
  to: string,
  returnNumber: string,
  locale = 'ja'
) {
  const { ExchangeRequestManualReceivedEmail } = await import(
    './templates/exchange-request-manual-received'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'exchange_request_manual_received',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeRequestManualReceivedEmail({ returnNumber, dictionary }),
  });
}

export async function sendExchangeRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    // New optional fields for PDF payload
    returnDate?: string;
    items?: Array<{ itemName: string; quantity: number; sku?: string }>;
  },
  locale = 'ja'
) {
  const { ExchangeRequestManualApprovedEmail } = await import(
    './templates/exchange-request-manual-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        returnDate: returnLabelData.returnDate,
        items: returnLabelData.items,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'exchange_request_manual_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeRequestManualApprovedEmail({ returnNumber, dictionary }),
    attachments,
  });
}

export async function sendRequestDeclinedEmail(
  to: string,
  returnNumber: string,
  locale = 'ja'
) {
  const { RequestDeclinedEmail } = await import('./templates/request-declined');
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject('request_declined', locale, {
    returnNumber,
  });

  return sendEmail({
    to,
    subject,
    template: RequestDeclinedEmail({ returnNumber, dictionary }),
  });
}

export async function sendAdminAddressChangeNotificationEmail(
  to: string,
  orderName: string,
  customerInfo: {
    firstName?: string;
    lastName?: string;
    email: string;
    phone?: string;
  },
  originalAddress: {
    firstName?: string;
    lastName?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    zip?: string;
    country?: string;
    phone?: string;
  },
  newAddress: {
    firstName?: string;
    lastName?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    zip?: string;
    country?: string;
    phone?: string;
  },
  locale = 'ja'
) {
  const { AdminAddressChangeNotificationTemplate } = await import(
    './templates/admin-address-change-notification'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'admin_address_change_notification',
    locale,
    { orderName }
  );

  return sendEmail({
    to,
    subject,
    template: AdminAddressChangeNotificationTemplate({
      orderName,
      customerInfo,
      originalAddress,
      newAddress,
      dictionary,
    }),
  });
}

export async function sendAdminRequestNotificationEmail(
  to: string,
  orderName: string,
  customerInfo: {
    firstName?: string;
    lastName?: string;
    email: string;
    phone?: string;
  },
  requestType: string,
  requestDetails: string,
  returnItems: any[],
  locale = 'ja'
) {
  const { AdminRequestNotificationTemplate } = await import(
    './templates/admin-request-notification'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'admin_request_notification',
    locale,
    { orderName, requestType }
  );

  return sendEmail({
    to,
    subject,
    template: AdminRequestNotificationTemplate({
      orderName,
      customerInfo,
      requestType,
      requestDetails,
      returnItems,
      dictionary,
    }),
  });
}

export async function sendSendersShareEmail(
  to: string,
  shareId: string,
  senderEmail: string,
  title: string,
  message: string,
  fileCount: number,
  expiresAt: string,
  isPrivate: boolean,
  locale = 'ja'
) {
  const { SendersShareEmail } = await import('./templates/senders-share');
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject('senders_share', locale, {
    isPrivate: String(isPrivate),
  });

  return sendEmail({
    to,
    subject,
    template: SendersShareEmail({
      shareId,
      senderEmail,
      title,
      message,
      fileCount,
      expiresAt,
      isPrivate,
      dictionary,
    }),
  });
}

export async function sendSendersConfirmationEmail(
  to: string,
  shareId: string,
  title: string,
  message: string,
  fileCount: number,
  expiresAt: string,
  isPrivate: boolean,
  locale = 'ja'
) {
  const { SendersConfirmationEmail } = await import(
    './templates/senders-confirmation'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );
  const dictionary = await loadEmailTranslations(locale);

  const subject = await getLocalizedEmailSubject(
    'senders_confirmation',
    locale,
    {}
  );

  return sendEmail({
    to,
    subject,
    template: SendersConfirmationEmail({
      shareId,
      title,
      message,
      fileCount,
      expiresAt,
      isPrivate,
      dictionary,
    }),
  });
}
