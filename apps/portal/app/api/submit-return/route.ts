import { env } from '@/env';
import { createAdminNotification, createReturnRequest } from '@/lib/db';
import { prisma } from '@/lib/prisma';
import { getOrderByName } from '@/lib/shopify';
import { getReturnNumber } from '@/lib/utils';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { orderName, email, orderItems, returnReason } = data;

    log.info(`Processing return/exchange for order ${orderName}`);

    if (!orderName || !email || !orderItems || !returnReason) {
      return NextResponse.json(
        { error: 'Order name, email, items, and reason are required' },
        { status: 400 }
      );
    }

    // Verify the order with email
    const order = await getOrderByName(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
          key: 'order_not_found_full',
        },
        { status: 200 }
      );
    }

    // Check if email is blacklisted
    const isBlacklisted = await prisma.blacklistEmail.findUnique({
      where: { email },
    });

    // Check for excessive returns and auto-blacklist if needed
    if (!isBlacklisted) {
      const returnCount = await prisma.returnRequest.count({
        where: { email },
      });

      if (returnCount >= 3) {
        // Auto-blacklist the email
        await prisma.blacklistEmail.create({
          data: {
            email,
            reason: 'excessive_returns',
          },
        });

        log.info(`Auto-blacklisted email ${email} for excessive returns`);

        // Create admin notification
        await createAdminNotification({
          type: 'excessive_returns',
          message: `Email ${email} has been blacklisted for excessive returns`,
          relatedOrderName: orderName,
          emailBody: JSON.stringify({
            type: 'admin-request-notification',
            to: env.ADMIN_EMAIL,
            returnNumber: orderName,
            orderName,
            customerInfo: {
              firstName: order.shippingAddress?.firstName || '',
              lastName: order.shippingAddress?.lastName || '',
              email,
              phone: order.shippingAddress?.phone || '',
            },
            priority: 8,
          }),
        });
      }
    }

    // Determine approval status
    let approvalStatus = 'pending';

    // If blacklisted, don't auto-approve
    const finalBlacklistCheck = await prisma.blacklistEmail.findUnique({
      where: { email },
    });

    if (finalBlacklistCheck) {
      log.info(`Return not auto-approved for blacklisted email: ${email}`);
    } else {
      // Check auto-approval settings
      const autoApprovalSettings = await prisma.appSettings.findUnique({
        where: { key: 'auto_approval_days' },
      });

      const autoApprovalDays = autoApprovalSettings
        ? Number.parseInt(autoApprovalSettings.value, 10)
        : 30; // Default to 30 days

      // Check if order is within auto-approval window
      const orderDate = new Date(order.createdAt);
      const currentDate = new Date();
      const daysDifference = Math.floor(
        (currentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDifference <= autoApprovalDays) {
        approvalStatus = 'approved';
        log.info(
          `Auto-approved return for order ${orderName} (${daysDifference} days old)`
        );
      } else {
        log.info(
          `Return requires manual approval for order ${orderName} (${daysDifference} days old, limit: ${autoApprovalDays})`
        );
      }
    }

    // Prepare data for database
    const returnItems = orderItems.map((item) => ({
      lineItemId: item.id,
      title: item.title,
      variantTitle: item.variantTitle,
      quantity: item.quantity,
      price: Number.parseFloat(item.price),
      currency: item.currency,
      sku: item.sku,
      barcode: item.barcode,
      returnReason: returnReason,
    }));

    const defectPhotos = data.defectPhotos
      ? data.defectPhotos.map((url) => ({ url }))
      : [];

    // Save the return request to the database
    const savedRequest = await createReturnRequest({
      orderName,
      email,
      returnNumber: getReturnNumber(orderName),
      returnReason,
      exchangeType: data.exchangeType,
      defectType: data.defectType,
      defectDetails: data.defectDetails,
      returnLabelOption: data.returnLabelOption,
      refundFee: data.refundFee,
      processed: 'pending',
      status: approvalStatus, // Use calculated approval status
      returnItems: {
        create: returnItems,
      },
      defectPhotos: {
        create: defectPhotos,
      },
    });

    // Create admin notification
    await createAdminNotification({
      type: 'return_request',
      message: `New ${data.exchangeType || 'return'} request for order ${orderName}`,
      relatedOrderName: orderName,
    });

    // Send appropriate email based on approval status
    try {
      const returnNumber = getReturnNumber(orderName);
      const isExchange = data.exchangeType === 'exchange';

      if (approvalStatus === 'approved') {
        // Auto-approved - send approval email
        const emailType = isExchange
          ? 'exchange-request-auto-approved'
          : 'return-request-auto-approved';

        const emailData = {
          type: emailType,
          to: email,
          returnNumber,
          ...(isExchange ? {} : { refundDays: '5-7' }),
          // Add return label data for PDF generation
          returnLabelOption: data.returnLabelOption,
          orderName,
          customerName: order.shippingAddress
            ? `${order.shippingAddress.firstName} ${order.shippingAddress.lastName}`
            : undefined,
          customerAddress: order.shippingAddress
            ? `${order.shippingAddress.address1}, ${order.shippingAddress.city}, ${order.shippingAddress.province} ${order.shippingAddress.zip}`
            : undefined,
        };

        const emailResponse = await fetch(
          `${env.NEXT_PUBLIC_API_URL}/api/email-queue/add`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${env.API_SECRET_KEY}`,
            },
            body: JSON.stringify({ ...emailData, priority: 8 }),
          }
        );

        if (emailResponse.ok) {
          const result = await emailResponse.json();
          log.info(`Queued ${emailType} email for ${email}`, {
            jobId: result.jobId,
          });
        } else {
          const errorText = await emailResponse.text();
          log.error('Failed to queue approval email:', { error: errorText });
        }
      } else {
        // Manual review - send received email
        const emailType = isExchange
          ? 'exchange-request-manual-received'
          : 'return-request-manual-received';

        const emailData = {
          type: emailType,
          to: email,
          returnNumber,
          ...(isExchange ? {} : { reviewDays: '3' }),
        };

        const emailResponse = await fetch(
          `${env.NEXT_PUBLIC_API_URL}/api/email-queue/add`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${env.API_SECRET_KEY}`,
            },
            body: JSON.stringify({ ...emailData, priority: 7 }),
          }
        );

        if (emailResponse.ok) {
          const result = await emailResponse.json();
          log.info(`Queued ${emailType} email for ${email}`, {
            jobId: result.jobId,
          });
        } else {
          const errorText = await emailResponse.text();
          log.error('Failed to queue received email:', { error: errorText });
        }
      }
    } catch (emailError) {
      log.error('Error queueing email:', { error: emailError });
      // Don't fail the request if email queueing fails
    }

    log.info('Return request saved:', savedRequest);

    return NextResponse.json(
      {
        success: true,
        message: 'Return request submitted successfully',
        returnNumber: getReturnNumber(orderName),
        isBlacklisted: finalBlacklistCheck != null,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error processing return/exchange:', { error });
    return NextResponse.json(
      { error: 'An error occurred while processing the return/exchange' },
      { status: 500 }
    );
  }
}
