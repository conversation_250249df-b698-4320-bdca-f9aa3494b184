'use client';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import type { Dictionary } from '@repo/internationalization';
import type { ReactNode } from 'react';
import * as z from 'zod';

// Define the form schema - we'll use a function to get the error message
const createFormSchema = (errorMessage: string) =>
  z.object({
    recipientName: z.string().min(1, errorMessage),
    note: z.string().optional(),
  });

// Default schema for type inference
const defaultFormSchema = createFormSchema('Recipient name is required');
export type ReceiptFormValues = z.infer<typeof defaultFormSchema>;

interface ReceiptFormProps {
  defaultValues: {
    recipientName: string;
    note?: string;
  };
  onSubmit: (values: ReceiptFormValues) => void;
  dictionary?: Dictionary | null;
  disabled?: boolean;
  actionsLeft?: ReactNode;
}

export default function ReceiptForm({
  defaultValues,
  onSubmit,
  dictionary,
  disabled = false,
  actionsLeft,
}: ReceiptFormProps) {
  // Default text if dictionary is not loaded yet
  const t = dictionary?.receipt?.form || {
    recipient_name: 'Recipient/Company Name',
    recipient_placeholder: 'Casefinite Inc.',
    recipient_description: 'The name that will appear on the receipt',
    note_name: 'Note',
    note_placeholder: 'For the cost of goods',
    note_description: 'Input example: "As payment for goods".',
    generate: 'Generate Receipt',
    recipient_required: 'Recipient name is required',
  };

  // Create form schema with translated error message
  const formSchema = createFormSchema(t.recipient_required);

  // Initialize the form
  const form = useForm<ReceiptFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // Handle form submission
  function handleSubmit(values: ReceiptFormValues) {
    onSubmit(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="recipientName"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>{t.recipient_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.recipient_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.recipient_description}</FormDescription>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>{t.note_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.note_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.note_description}</FormDescription>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="flex items-center justify-between gap-3">
          <div>{actionsLeft}</div>
          <Button type="submit" className="cursor-pointer">
            {t.generate}
          </Button>
        </div>
      </form>
    </Form>
  );
}
