'use client';

import { Loader2, Trash } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import type React from 'react';
import { type ComponentPropsWithoutRef, forwardRef, useState } from 'react';

export interface DeleteConfirmationDialogProps
  extends ComponentPropsWithoutRef<typeof Button> {
  title: string;
  description: string;
  deleteButtonText?: string;
  deletingText?: string;
  onDelete: () => Promise<void>;
  onSuccess?: () => void;
  onError?: () => void;
}

export const DeleteConfirmationDialog = forwardRef<
  HTMLButtonElement,
  DeleteConfirmationDialogProps
>(
  (
    {
      title,
      description,
      deleteButtonText,
      deletingText,
      onDelete,
      onSuccess,
      onError,
      disabled,
      ...buttonProps
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const handleDelete = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsOpen(true);
    };

    const handleConfirm = async () => {
      setIsLoading(true);

      try {
        await onDelete();
        onSuccess?.();
      } catch (error) {
        console.error('Error during deletion:', error);
        onError?.();
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    };

    return (
      <>
        <Button
          ref={ref}
          {...buttonProps}
          onClick={handleDelete}
          disabled={isLoading || disabled}
        >
          <Trash className="text-destructive" />
        </Button>

        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{title}</AlertDialogTitle>
              <AlertDialogDescription>{description}</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                variant="destructive"
                onClick={handleConfirm}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {deletingText}
                  </>
                ) : (
                  deleteButtonText
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }
);
