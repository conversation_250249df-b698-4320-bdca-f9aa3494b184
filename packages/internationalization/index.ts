import 'server-only';
import type ja from './dictionaries/ja.json';
import languine from './languine.json';

export const locales = [
  languine.locale.source,
  ...languine.locale.targets,
] as const;

export type Dictionary = typeof ja;

const dictionaries: Record<string, () => Promise<Dictionary>> =
  Object.fromEntries(
    locales.map((locale) => [
      locale,
      () =>
        import(`./dictionaries/${locale}.json`)
          .then((mod) => mod.default)
          .catch((err) => {
            console.error(
              `Failed to load dictionary for locale: ${locale}`,
              err
            );
            return import('./dictionaries/ja.json').then((mod) => mod.default);
          }),
    ])
  );

export const getDictionary = async (locale: string): Promise<Dictionary> => {
  const normalizedLocale = locale.split('-')[0];

  if (!locales.includes(normalizedLocale)) {
    console.warn(`Locale "${locale}" is not supported, defaulting to "ja"`);
    return dictionaries.ja();
  }

  try {
    return await dictionaries[normalizedLocale]();
  } catch (error) {
    console.error(
      `Error loading dictionary for locale "${normalizedLocale}", falling back to "ja"`,
      error
    );
    return dictionaries.ja();
  }
};

export const japaneseProvinceMapping = {
  // Hokkaido Region
  '01': '北海道',
  'JP-01': '北海道',
  Hokkaido: '北海道',

  // Tohoku Region
  '02': '青森県',
  'JP-02': '青森県',
  Aomori: '青森県',

  '03': '岩手県',
  'JP-03': '岩手県',
  Iwate: '岩手県',

  '04': '宮城県',
  'JP-04': '宮城県',
  Miyagi: '宮城県',

  '05': '秋田県',
  'JP-05': '秋田県',
  Akita: '秋田県',

  '06': '山形県',
  'JP-06': '山形県',
  Yamagata: '山形県',

  '07': '福島県',
  'JP-07': '福島県',
  Fukushima: '福島県',

  // Kanto Region
  '08': '茨城県',
  'JP-08': '茨城県',
  Ibaraki: '茨城県',

  '09': '栃木県',
  'JP-09': '栃木県',
  Tochigi: '栃木県',

  '10': '群馬県',
  'JP-10': '群馬県',
  Gunma: '群馬県',

  '11': '埼玉県',
  'JP-11': '埼玉県',
  Saitama: '埼玉県',

  '12': '千葉県',
  'JP-12': '千葉県',
  Chiba: '千葉県',

  '13': '東京都',
  'JP-13': '東京都',
  Tokyo: '東京都',

  '14': '神奈川県',
  'JP-14': '神奈川県',
  Kanagawa: '神奈川県',

  // Chubu Region
  '15': '新潟県',
  'JP-15': '新潟県',
  Niigata: '新潟県',

  '16': '富山県',
  'JP-16': '富山県',
  Toyama: '富山県',

  '17': '石川県',
  'JP-17': '石川県',
  Ishikawa: '石川県',

  '18': '福井県',
  'JP-18': '福井県',
  Fukui: '福井県',

  '19': '山梨県',
  'JP-19': '山梨県',
  Yamanashi: '山梨県',

  '20': '長野県',
  'JP-20': '長野県',
  Nagano: '長野県',

  '21': '岐阜県',
  'JP-21': '岐阜県',
  Gifu: '岐阜県',

  '22': '静岡県',
  'JP-22': '静岡県',
  Shizuoka: '静岡県',

  '23': '愛知県',
  'JP-23': '愛知県',
  Aichi: '愛知県',

  // Kansai Region
  '24': '三重県',
  'JP-24': '三重県',
  Mie: '三重県',

  '25': '滋賀県',
  'JP-25': '滋賀県',
  Shiga: '滋賀県',

  '26': '京都府',
  'JP-26': '京都府',
  Kyoto: '京都府',

  '27': '大阪府',
  'JP-27': '大阪府',
  Osaka: '大阪府',

  '28': '兵庫県',
  'JP-28': '兵庫県',
  Hyogo: '兵庫県',

  '29': '奈良県',
  'JP-29': '奈良県',
  Nara: '奈良県',

  '30': '和歌山県',
  'JP-30': '和歌山県',
  Wakayama: '和歌山県',

  // Chugoku Region
  '31': '鳥取県',
  'JP-31': '鳥取県',
  Tottori: '鳥取県',

  '32': '島根県',
  'JP-32': '島根県',
  Shimane: '島根県',

  '33': '岡山県',
  'JP-33': '岡山県',
  Okayama: '岡山県',

  '34': '広島県',
  'JP-34': '広島県',
  Hiroshima: '広島県',

  '35': '山口県',
  'JP-35': '山口県',
  Yamaguchi: '山口県',

  // Shikoku Region
  '36': '徳島県',
  'JP-36': '徳島県',
  Tokushima: '徳島県',

  '37': '香川県',
  'JP-37': '香川県',
  Kagawa: '香川県',

  '38': '愛媛県',
  'JP-38': '愛媛県',
  Ehime: '愛媛県',

  '39': '高知県',
  'JP-39': '高知県',
  Kochi: '高知県',

  // Kyushu Region
  '40': '福岡県',
  'JP-40': '福岡県',
  Fukuoka: '福岡県',

  '41': '佐賀県',
  'JP-41': '佐賀県',
  Saga: '佐賀県',

  '42': '長崎県',
  'JP-42': '長崎県',
  Nagasaki: '長崎県',

  '43': '熊本県',
  'JP-43': '熊本県',
  Kumamoto: '熊本県',

  '44': '大分県',
  'JP-44': '大分県',
  Oita: '大分県',

  '45': '宮崎県',
  'JP-45': '宮崎県',
  Miyazaki: '宮崎県',

  '46': '鹿児島県',
  'JP-46': '鹿児島県',
  Kagoshima: '鹿児島県',

  // Okinawa Region
  '47': '沖縄県',
  'JP-47': '沖縄県',
  Okinawa: '沖縄県',

  // Common romanized versions that might appear
  Tōkyō: '東京都',
  Ōsaka: '大阪府',
  Kyōto: '京都府',
};
