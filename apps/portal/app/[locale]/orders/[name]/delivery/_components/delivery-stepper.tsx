import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';

// Import the shared interface from the parent component
interface DeliveryStatusData {
  logisticStatus?: number;
  currentStep: number;
  deliveryStatus: string;
  isLoading: boolean;
  issueDetected?: boolean;
}

export function DeliveryStepper({
  dictionary,
  deliveryData,
}: {
  dictionary: Dictionary;
  deliveryData: DeliveryStatusData;
}) {
  // Use the delivery data passed from parent component
  const { currentStep, isLoading } = deliveryData;

  if (currentStep === 0 || isLoading) {
    return (
      <div className="space-y-1">
        <div className="flex w-full justify-between space-x-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
        <div className="flex w-full justify-between space-x-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    );
  }

  return (
    <Stepper defaultValue={currentStep} orientation="horizontal">
      <StepperItem step={1} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">1</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 1 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.processing}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={2} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">2</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 2 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.preparing_for_delivery}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={3} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">3</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 3 && 'font-bold',
            currentStep === 3 &&
              (deliveryData.issueDetected ? 'text-red-600' : 'text-primary')
          )}
        >
          {dictionary.delivery.in_transit}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={4} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">4</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 4 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.delivered}
        </StepperTitle>
      </StepperItem>
    </Stepper>
  );
}
