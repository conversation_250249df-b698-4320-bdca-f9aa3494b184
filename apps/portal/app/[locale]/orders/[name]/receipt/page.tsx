import { env } from '@/env';
import { getOrderByName } from '@/lib/shopify';
import { getDictionary } from '@repo/internationalization';
import { redirect } from 'next/navigation';
import ReceiptDialog from './_components/receipt-dialog';

interface ReceiptPageProps {
  params: Promise<{
    name: string;
    locale: string;
  }>;
  searchParams: Promise<{
    email?: string;
  }>;
}

export default async function ReceiptPage({
  params,
  searchParams,
}: ReceiptPageProps) {
  const { name, locale } = await params;
  const { email } = await searchParams;

  const dictionary = await getDictionary(locale);

  // Verify order access with email if provided
  const order = await getOrderByName(name, email);

  // If order not found or email verification failed
  if (!order) {
    // Redirect to home page with error message, including the locale
    redirect(
      `/${locale}?error=verification_failed&orderName=${encodeURIComponent(name)}`
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="font-bold text-3xl uppercase tracking-tight">
          {env.NEXT_PUBLIC_SITE_TITLE ?? 'Return & Exchange Portal'}
        </h1>
      </div>
      <ReceiptDialog
        orderName={order.name}
        order={order}
        dictionary={dictionary}
        inline
      />
    </div>
  );
}
