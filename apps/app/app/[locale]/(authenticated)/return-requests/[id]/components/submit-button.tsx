'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { Dictionary } from '@repo/internationalization';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number;
}

interface SubmitButtonProps {
  exchangeType: string | null;
  exchangeStep?: 'outgoing' | 'incoming';
  scanningStep?: 'package' | 'items';
  returnPackageVerified?: boolean;
  allScanned: boolean;
  productChecklist: ReturnItem[];
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
  dictionary: Dictionary;
}

export function SubmitButton({
  exchangeType,
  exchangeStep,
  returnPackageVerified,
  allScanned,
  productChecklist,
  onSubmit,
  dictionary,
}: SubmitButtonProps) {
  const shouldShowButton =
    ((exchangeType !== 'exchange' &&
      (exchangeType !== 'return' || (returnPackageVerified && allScanned))) ||
      (exchangeType === 'exchange' && allScanned)) &&
    onSubmit;

  if (!shouldShowButton) {
    return null;
  }

  const getButtonText = () => {
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return dictionary.admin.scanner.submit_button.confirm_exchange_shipped;
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return dictionary.admin.scanner.submit_button.confirm_return_received;
    }
    return dictionary.admin.scanner.submit_button.complete_verification;
  };

  const handleSubmit = () => {
    const scannedItems = productChecklist.map((item) => ({
      itemId: item.id,
      scannedQuantity: item.currentQty || 0,
    }));

    onSubmit?.(scannedItems);

    let title = dictionary.admin.scanner.submit_button.success;
    let description = dictionary.admin.scanner.submit_button.all_items_verified;

    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      title = dictionary.admin.scanner.submit_button.outgoing_items_confirmed;
      description =
        dictionary.admin.scanner.submit_button.outgoing_items_description;
    } else if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      title = dictionary.admin.scanner.submit_button.incoming_items_received;
      description =
        dictionary.admin.scanner.submit_button.incoming_items_description;
    }

    toast.success(title, { description });
  };

  return (
    <div className="flex justify-center pt-4">
      <Button onClick={handleSubmit} className="w-full">
        {getButtonText()}
      </Button>
    </div>
  );
}
