'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { formatDate } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import type { ColumnDef } from '@tanstack/react-table';

// Define the AddressChangeRequest type based on what we're selecting in the action
export type AddressChangeRow = {
  id: string;
  orderName: string;
  email: string;
  status: string;
  createdAt: string;
  updatedAt: string;
};

export const columns = (
  dictionary: Dictionary
): ColumnDef<AddressChangeRow>[] => [
  {
    accessorKey: 'orderName',
    header: dictionary.admin.address_changes.order_name,
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('orderName')}</div>
    ),
  },
  {
    accessorKey: 'email',
    header: dictionary.admin.address_changes.email,
  },
  {
    accessorKey: 'status',
    header: dictionary.admin.address_changes.status,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      let variant: 'default' | 'success' | 'destructive' | 'secondary' =
        'secondary';
      let text = dictionary.admin.return_requests.status_pending;

      if (status === 'completed') {
        variant = 'default';
        text = dictionary.admin.address_changes.status_completed;
      } else if (status === 'approved') {
        variant = 'success';
        text = dictionary.admin.address_changes.status_approved;
      } else if (status === 'rejected') {
        variant = 'destructive';
        text = dictionary.admin.address_changes.status_rejected;
      }

      return <Badge variant={variant}>{text}</Badge>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: dictionary.admin.address_changes.created_at,
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
  {
    accessorKey: 'updatedAt',
    header: dictionary.admin.address_changes.updated_at,
    cell: ({ row }) => {
      const date = new Date(row.getValue('updatedAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
];
