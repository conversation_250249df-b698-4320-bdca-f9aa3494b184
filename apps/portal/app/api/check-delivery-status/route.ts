import { env } from '@/env';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const { trackingUrl, keyword }: { trackingUrl: string; keyword: string } =
    await request.json();

  log.info(`Checking delivery status for tracking URL: ${trackingUrl}`);

  if (!keyword) {
    return NextResponse.json({ error: 'Keyword is required' }, { status: 400 });
  }

  if (!trackingUrl) {
    return NextResponse.json(
      { error: 'Tracking URL is required' },
      { status: 400 }
    );
  }

  try {
    const response = await fetch(
      'https://orders-staging-api.casefinite.jp/api/v1/admin/scrape',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'access-secret-token': env.CASEFINITE_API_KEY,
        },
        body: JSON.stringify({
          url: trackingUrl,
          keyword,
        }),
      }
    );

    const result = await response.json();

    return NextResponse.json({ data: result.data }, { status: 200 });
  } catch (error) {
    log.error('Error checking delivery status:', { error });
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check delivery status',
      },
      { status: 200 }
    );
  }
}
