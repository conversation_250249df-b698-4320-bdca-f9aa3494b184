'use client';

import { CmsContent } from '@/components/cms-content';
import { env } from '@/env';
import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { CardFooter } from '@repo/design-system/components/ui/card';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { dateFormatter as format } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Package,
  Truck,
} from 'lucide-react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { DeliveryStepper } from './_components/delivery-stepper';

interface DeliveryPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

// Helper function to format date
const formatDate = (
  dateString: string | null | undefined,
  notAvailable = 'N/A',
  invalidDate = 'Invalid date'
): string => {
  if (!dateString) {
    return notAvailable;
  }
  try {
    return format(new Date(dateString), 'yyyy-MM-dd');
  } catch (e) {
    log.error('Error formatting date:', { e });
    return invalidDate;
  }
};

// Shared delivery status data interface
interface DeliveryStatusData {
  logisticStatus?: number;
  currentStep: number;
  deliveryStatus: string;
  isLoading: boolean;
  // Indicates if there's a potential issue with the shipment (e.g., returned/investigating)
  issueDetected?: boolean;
  issueMessage?: string;
}

// Custom hook for managing delivery status
function useDeliveryStatus(
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>,
  dictionary: Dictionary
): DeliveryStatusData {
  const [logisticStatus, setLogisticStatus] = useState<number | undefined>();
  const [currentStep, setCurrentStep] = useState(0);
  const [deliveryStatus, setDeliveryStatus] = useState('processing');
  const [isLoading, setIsLoading] = useState(true);
  const [issueDetected, setIssueDetected] = useState(false);
  const [issueMessage, setIssueMessage] = useState<string | undefined>();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (env.NEXT_PUBLIC_REGION === 'JP') {
      const fetchJpStatus = async () => {
        setIsLoading(true);

        try {
          const response = await fetch('/api/check-logistic-status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              orderName: order.name.replace('#', ''),
              email: order.email,
            }),
          });

          if (response.ok) {
            const data = (await response.json()) as {
              logisticStatus: number;
            };

            setLogisticStatus(data.logisticStatus);

            // Determine step and status based on logistic status
            let step = 1;
            let status = 'processing';

            if (data.logisticStatus >= 4) {
              step = 2;
              status = 'preparing_for_delivery';
            }
            if (data.logisticStatus >= 5) {
              step = 3;
              status = 'in_transit';
            }
            if (data.logisticStatus >= 7) {
              step = 4;
              status = 'delivered';
            }

            // Check for actual delivery if in transit
            if (step === 3) {
              const trackingUrl = order.fulfillments?.find(
                (f) => f.trackingInfo && f.trackingInfo.length > 0
              )?.trackingInfo?.[0]?.url;

              if (trackingUrl) {
                try {
                  const deliveryResponse = await fetch(
                    '/api/check-delivery-status',
                    {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({
                        trackingUrl,
                        keyword: [
                          'お届け先にお届け済み',
                          '配達完了',
                          '調査中',
                          '返品',
                          '返品完了',
                        ],
                      }),
                    }
                  );

                  if (deliveryResponse.ok) {
                    const deliveryData = (await deliveryResponse.json()) as {
                      data: {
                        results: {
                          found: boolean;
                          keyword: string;
                        }[];
                      };
                    };

                    // find if results match 'お届け先にお届け済み' or '配達完了' with found: true
                    const isDelivered = deliveryData.data.results.some(
                      (result) =>
                        ['お届け先にお届け済み', '配達完了'].includes(
                          result.keyword
                        ) && result.found === true
                    );

                    if (isDelivered) {
                      step = 4;
                      status = 'delivered';
                    }

                    // find if results match '調査中', '返品', '返品完了'
                    const isReturned = deliveryData.data.results.some(
                      (result) =>
                        ['調査中', '返品', '返品完了'].includes(
                          result.keyword
                        ) && result.found === true
                    );

                    // If shipment is being investigated/returned, keep at step 3 but flag issue
                    if (isReturned) {
                      step = 3;
                      status = 'in_transit';
                      setIssueDetected(true);
                      setIssueMessage(dictionary.delivery.issue_found_message);
                    }
                  }
                } catch (error) {
                  log.error('Error checking delivery status:', { error });
                }
              }
            }

            setCurrentStep(step);
            setDeliveryStatus(status);
          }
        } catch (error) {
          log.error('Error fetching JP delivery status:', { error });
          setCurrentStep(1); // Default to first step on error
        } finally {
          setIsLoading(false);
        }
      };

      fetchJpStatus();
    }
  }, [order]);

  return {
    logisticStatus,
    currentStep,
    deliveryStatus,
    isLoading,
    issueDetected,
    issueMessage,
  };
}

export default function DeliveryPageClient({
  order,
  dictionary,
}: DeliveryPageClientProps) {
  const router = useRouter();
  const { locale } = useParams();

  // Use the shared delivery status hook
  const jpDeliveryData = useDeliveryStatus(order, dictionary);

  const handleBack = () => {
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  // Analyze all fulfillments
  const fulfillments = order.fulfillments || [];
  const sortedFulfillments = [...fulfillments].sort((a, b) => {
    const dateA = a.updatedAt || a.createdAt;
    const dateB = b.updatedAt || b.createdAt;
    return new Date(dateB).getTime() - new Date(dateA).getTime();
  });

  // Determine overall delivery status based on region
  const getOverallDeliveryStatus = () => {
    // For JP region, use the fetched JP delivery status
    // For GLOBAL region, use Shopify's displayFulfillmentStatus
    if (env.NEXT_PUBLIC_REGION === 'JP') {
      return jpDeliveryData.deliveryStatus;
    }

    // GLOBAL region logic using Shopify status
    const shopifyStatus = order.displayFulfillmentStatus;

    switch (shopifyStatus) {
      case 'FULFILLED':
        return 'delivered';
      case 'PARTIALLY_FULFILLED':
        return 'partially_fulfilled';
      case 'IN_PROGRESS': {
        // Check if any fulfillments are in transit
        const hasInTransit = fulfillments.some(
          (f) => f.inTransitAt || f.status.toLowerCase().includes('transit')
        );
        return hasInTransit ? 'in_transit' : 'processing';
      }
      case 'ON_HOLD':
      case 'SCHEDULED':
        return 'processing';
      case 'REQUEST_DECLINED':
        return 'failed';
      case 'UNFULFILLED':
      case 'OPEN':
      case 'RESTOCKED':
        return 'pending';
      default:
        return 'pending';
    }
  };

  const hasMultipleFulfillments = fulfillments.length > 1;
  const latestFulfillment = sortedFulfillments[0] || null;

  const getStatusIcon = (status: string) => {
    // If an issue (e.g., returned/investigation) is detected, always show error icon
    if (jpDeliveryData.issueDetected) {
      return <AlertCircle className="h-6 w-6 text-red-500" />;
    }

    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'partially_fulfilled':
        return <Package className="h-6 w-6 text-orange-500" />;
      case 'in_transit':
        return <Truck className="h-6 w-6 text-blue-500" />;
      case 'preparing_for_delivery':
        return <Package className="h-6 w-6 text-blue-500" />;
      case 'attempted':
        return <AlertCircle className="h-6 w-6 text-yellow-500" />;
      case 'failed':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      default:
        return <Package className="h-6 w-6 text-muted-foreground" />;
    }
  };

  // Get status display text
  const getStatusDisplayText = (status: string): string => {
    // If an issue (e.g., returned/investigation) is detected, override title
    if (jpDeliveryData.issueDetected) {
      return dictionary.delivery.issue_found_title;
    }

    switch (status) {
      case 'delivered':
        return dictionary.delivery.delivered;
      case 'partially_fulfilled':
        return dictionary.delivery.partially_fulfilled;
      case 'in_transit':
        return dictionary.delivery.in_transit;
      case 'preparing_for_delivery':
        return dictionary.delivery.preparing_for_delivery;
      case 'attempted':
        return dictionary.delivery.attempted;
      case 'failed':
        return dictionary.delivery.failed;
      case 'processing':
        return dictionary.delivery.processing;
      case 'cancelled':
        return dictionary.delivery.cancelled;
      default:
        return dictionary.delivery.pending;
    }
  };

  // Use overall status for display, but override label when issue is detected
  const deliveryStatus = getOverallDeliveryStatus();

  // Get additional status context
  const getStatusContext = () => {
    switch (order.displayFulfillmentStatus) {
      case 'PARTIALLY_FULFILLED':
        return dictionary.delivery.some_items_delivered;
      case 'IN_PROGRESS':
        return dictionary.delivery.processing;
      case 'ON_HOLD':
        return 'Order is on hold';
      case 'SCHEDULED':
        return 'Fulfillment is scheduled';
      case 'REQUEST_DECLINED':
        return 'Fulfillment request was declined';
      default:
        return null;
    }
  };

  // Format estimated delivery date
  const estimatedDelivery = latestFulfillment?.estimatedDeliveryAt
    ? formatDate(
        latestFulfillment.estimatedDeliveryAt,
        dictionary.delivery.not_available,
        dictionary.delivery.invalid_date
      )
    : dictionary.delivery.not_available;

  return (
    <div className="mx-auto max-w-2xl space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.return.delivery_confirmation}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {env.NEXT_PUBLIC_REGION === 'JP' && (
            <DeliveryStepper
              dictionary={dictionary}
              deliveryData={jpDeliveryData}
            />
          )}

          {/* Optional CMS note */}
          <CmsContent
            contentKey="delivery_note"
            locale={locale as string}
            fallback=""
          />

          {fulfillments.length === 0 && (
            <Alert>
              <AlertDescription>
                {dictionary.delivery.no_information}
              </AlertDescription>
            </Alert>
          )}

          {jpDeliveryData.isLoading ? (
            <div className="flex w-full justify-between space-x-2">
              <Skeleton className="h-20 w-full" />
            </div>
          ) : (
            // Overall Status Summary
            <div className="flex items-center gap-4 rounded-lg bg-muted p-4">
              {getStatusIcon(deliveryStatus)}
              <div className="flex-1">
                <p
                  className={cn(
                    'font-medium',
                    jpDeliveryData.issueDetected ? 'text-red-600' : ''
                  )}
                >
                  {getStatusDisplayText(deliveryStatus)}
                </p>
                {jpDeliveryData.issueDetected ? (
                  <p className="text-muted-foreground text-sm">
                    {jpDeliveryData.issueMessage ||
                      dictionary.delivery.issue_found_message}
                  </p>
                ) : (
                  getStatusContext() && (
                    <p className="text-muted-foreground text-sm">
                      {getStatusContext()}
                    </p>
                  )
                )}
                {hasMultipleFulfillments && (
                  <p className="text-muted-foreground text-sm">
                    {dictionary.delivery.multiple_shipments}
                  </p>
                )}
                {!jpDeliveryData.issueDetected &&
                  deliveryStatus !== 'delivered' && (
                    <p className="text-muted-foreground text-sm">
                      {dictionary.delivery.estimated}: {estimatedDelivery}
                    </p>
                  )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <UnfulfilledSection
        lineItems={getUnfulfilledLineItems(order)}
        order={order}
        dictionary={dictionary}
      />

      <FulfilledSection
        fulfillments={sortedFulfillments}
        order={order}
        dictionary={dictionary}
      />

      <div className="flex justify-between space-x-4 text-center">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          {dictionary.button.back}
        </Button>
      </div>
    </div>
  );
}

function FulfilledSection({
  fulfillments,
  order,
  dictionary,
}: {
  fulfillments: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['fulfillments'];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}) {
  // Helper function to find original line item data
  const findOriginalLineItem = (fulfillmentLineItemId: string) => {
    return order.lineItems.edges.find(
      (edge) => edge.node.id === fulfillmentLineItemId
    )?.node;
  };

  return (
    <>
      {fulfillments.map((fulfillment) => {
        const fulfillmentTrackingInfo =
          fulfillment.trackingInfo && fulfillment.trackingInfo.length > 0
            ? fulfillment.trackingInfo[0]
            : null;

        return (
          <Card
            key={fulfillment.id}
            className={cn(order.shippingAddress && 'pb-0')}
          >
            <CardHeader>
              <CardTitle className="text-lg">
                {dictionary.delivery.fulfilled_items || 'Fulfilled Items'}
              </CardTitle>
              <CardDescription>
                {`${fulfillment.fulfillmentLineItems.edges.length} item${
                  fulfillment.fulfillmentLineItems.edges.length !== 1 ? 's' : ''
                } shipped`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {fulfillmentTrackingInfo && (
                <div className="mb-6 flex items-end justify-between rounded-lg border bg-muted/50 p-4">
                  <div>
                    <h3 className="font-medium">
                      {dictionary.delivery.tracking}
                    </h3>
                    <p className="text-sm">
                      {dictionary.delivery.carrier}:{' '}
                      {fulfillmentTrackingInfo.company ||
                        dictionary.delivery.not_available}
                    </p>
                    <p className="text-sm">
                      {dictionary.delivery.tracking_number}:{' '}
                      {fulfillmentTrackingInfo.number ? (
                        fulfillmentTrackingInfo.url ? (
                          <a
                            href={fulfillmentTrackingInfo.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 underline hover:text-blue-800"
                          >
                            {fulfillmentTrackingInfo.number}
                          </a>
                        ) : (
                          fulfillmentTrackingInfo.number
                        )
                      ) : (
                        dictionary.delivery.not_available
                      )}
                    </p>
                    <p className="text-sm">
                      {dictionary.delivery.created_on}:{' '}
                      {formatDate(fulfillment.createdAt)}
                    </p>
                  </div>
                  <Button size="sm" asChild>
                    <a
                      href={fulfillmentTrackingInfo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1"
                    >
                      <Truck className="h-3 w-3" />
                      {dictionary.delivery.track_package}
                    </a>
                  </Button>
                </div>
              )}

              <div className="space-y-6">
                {fulfillment.fulfillmentLineItems.edges.map((node) => {
                  const originalLineItem = findOriginalLineItem(
                    node.node.lineItem.id
                  );

                  return (
                    <div key={node.node.id} className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="relative h-22 w-22 overflow-hidden rounded-lg border bg-muted">
                          {node.node.lineItem.image ? (
                            <Image
                              src={node.node.lineItem.image.url}
                              alt={node.node.lineItem.name}
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-lg"
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                              <span className="text-xs">No Image</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex-grow">
                        <h4 className="font-medium text-base leading-tight">
                          {node.node.lineItem.name}
                        </h4>
                        {originalLineItem?.variantTitle && (
                          <p className="text-muted-foreground text-sm">
                            {originalLineItem.variantTitle}
                          </p>
                        )}
                        <div className="mt-2 flex flex-col">
                          {originalLineItem?.originalUnitPriceSet
                            ?.shopMoney && (
                            <span className="font-medium text-sm">
                              {new Intl.NumberFormat(
                                originalLineItem.originalUnitPriceSet.shopMoney
                                  .currencyCode === 'JPY'
                                  ? 'ja-JP'
                                  : 'en-US',
                                {
                                  style: 'currency',
                                  currency:
                                    originalLineItem.originalUnitPriceSet
                                      .shopMoney.currencyCode,
                                }
                              ).format(
                                Number(
                                  originalLineItem.originalUnitPriceSet
                                    .shopMoney.amount
                                )
                              )}
                            </span>
                          )}
                          <span className="text-muted-foreground text-xs">
                            {dictionary.order.quantity}:{' '}
                            {node.node.lineItem.quantity}
                          </span>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <Button size="sm" variant="outline">
                          {dictionary.delivery.return_item || 'Return Item'}
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
            {order.shippingAddress && (
              <CardFooter className="border-t bg-muted/50 pb-6">
                <div className="w-full">
                  <h5 className="mb-2 font-medium text-sm">
                    {dictionary.delivery.delivery_address}
                  </h5>
                  <div className="text-muted-foreground text-sm">
                    <p>
                      {order.shippingAddress.firstName}{' '}
                      {order.shippingAddress.lastName}
                    </p>
                    {order.shippingAddress.company && (
                      <p>{order.shippingAddress.company}</p>
                    )}
                    <p>{order.shippingAddress.address1}</p>
                    {order.shippingAddress.address2 && (
                      <p>{order.shippingAddress.address2}</p>
                    )}
                    <p>
                      {order.shippingAddress.city},{' '}
                      {order.shippingAddress.province}{' '}
                      {order.shippingAddress.zip}
                    </p>
                    <p>{order.shippingAddress.country}</p>
                    {order.shippingAddress.phone && (
                      <p>{order.shippingAddress.phone}</p>
                    )}
                  </div>
                </div>
              </CardFooter>
            )}
          </Card>
        );
      })}
    </>
  );
}

function UnfulfilledSection({
  lineItems,
  order,
  dictionary,
}: {
  lineItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'][number]['node'][];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}) {
  // Don't render if no unfulfilled items
  if (!lineItems || lineItems.length === 0) {
    return null;
  }

  return (
    <Card className={cn(order.shippingAddress && 'pb-0')}>
      <CardHeader>
        <CardTitle className="text-lg">
          {dictionary.delivery.unfulfilled_items}
        </CardTitle>
        <CardDescription>
          {dictionary.delivery.unfulfilled_description
            .replace('{count}', lineItems.length.toString())
            .replace('{plural}', lineItems.length !== 1 ? 's' : '')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {lineItems.map((lineItem) => {
          return (
            <div key={lineItem.id} className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <div className="relative h-22 w-22 overflow-hidden rounded-lg border bg-muted">
                  {lineItem.image ? (
                    <Image
                      src={lineItem.image.url}
                      alt={lineItem.name}
                      fill
                      style={{ objectFit: 'cover' }}
                      className="rounded-lg"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                      <span className="text-xs">No Image</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex-grow">
                <h4 className="font-medium text-base leading-tight">
                  {lineItem.name}
                </h4>
                {lineItem.variantTitle && (
                  <p className="text-muted-foreground text-sm">
                    {lineItem.variantTitle}
                  </p>
                )}
                <div className="mt-2 flex flex-col">
                  {lineItem.originalUnitPriceSet?.shopMoney && (
                    <span className="font-medium text-sm">
                      {new Intl.NumberFormat(
                        lineItem.originalUnitPriceSet.shopMoney.currencyCode ===
                          'JPY'
                          ? 'ja-JP'
                          : 'en-US',
                        {
                          style: 'currency',
                          currency:
                            lineItem.originalUnitPriceSet.shopMoney
                              .currencyCode,
                        }
                      ).format(
                        Number(lineItem.originalUnitPriceSet.shopMoney.amount)
                      )}
                    </span>
                  )}
                  <span className="text-muted-foreground text-xs">
                    {dictionary.order.quantity}: {lineItem.quantity}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
      {order.shippingAddress && (
        <CardFooter className="border-t bg-muted/50 pb-6">
          <div className="w-full">
            <h5 className="mb-2 font-medium text-sm">
              {dictionary.delivery.delivery_address}
            </h5>
            <div className="text-muted-foreground text-sm">
              <p>
                {order.shippingAddress.firstName}{' '}
                {order.shippingAddress.lastName}
              </p>
              {order.shippingAddress.company && (
                <p>{order.shippingAddress.company}</p>
              )}
              <p>{order.shippingAddress.address1}</p>
              {order.shippingAddress.address2 && (
                <p>{order.shippingAddress.address2}</p>
              )}
              <p>
                {order.shippingAddress.city}, {order.shippingAddress.province}{' '}
                {order.shippingAddress.zip}
              </p>
              <p>{order.shippingAddress.country}</p>
              {order.shippingAddress.phone && (
                <p>{order.shippingAddress.phone}</p>
              )}
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}

// Returns unfulfilled line items from order.lineItems
function getUnfulfilledLineItems(
  order: GetOrdersQuery['orders']['edges'][0]['node']
) {
  // Map lineItem.id to total fulfilled quantity
  const fulfilledQuantities = {};

  if (order.fulfillments) {
    for (const fulfillment of order.fulfillments) {
      if (fulfillment.fulfillmentLineItems) {
        for (const edge of fulfillment.fulfillmentLineItems.edges) {
          const { lineItem } = edge.node;
          if (lineItem?.id) {
            fulfilledQuantities[lineItem.id] =
              (fulfilledQuantities[lineItem.id] || 0) + lineItem.quantity;
          }
        }
      }
    }
  }

  // Filter line items where fulfilled quantity < ordered quantity
  const unfulfilledLineItems = order.lineItems.edges.filter((edge) => {
    const { id, quantity } = edge.node;
    return (fulfilledQuantities[id] || 0) < quantity;
  });

  return unfulfilledLineItems.map((edge) => edge.node);
}
