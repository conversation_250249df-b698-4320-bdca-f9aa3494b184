import { type Dictionary, getDictionary } from '@repo/internationalization';

// Email-specific dictionary type
export type EmailDictionary = Dictionary['email'];

// Supported locales for emails
export const EMAIL_LOCALES = ['ja', 'en'] as const;
export type EmailLocale = (typeof EMAIL_LOCALES)[number];

/**
 * Load dictionary for email context
 * @param locale - The locale to load (defaults to 'ja')
 * @returns Promise<Dictionary> - The loaded dictionary
 */
export async function getEmailDictionary(locale = 'ja'): Promise<Dictionary> {
  // Normalize locale (e.g., 'en-US' -> 'en')
  const normalizedLocale = locale.split('-')[0];

  // Validate locale and fallback to 'ja' if invalid
  const validLocale = EMAIL_LOCALES.includes(normalizedLocale as EmailLocale)
    ? normalizedLocale
    : 'ja';

  try {
    return await getDictionary(validLocale);
  } catch (error) {
    console.error(
      `Failed to load email dictionary for locale "${validLocale}", falling back to "ja"`,
      error
    );
    return await getDictionary('ja');
  }
}

/**
 * Get email-specific translations from dictionary
 * @param dictionary - The full dictionary object
 * @returns EmailDictionary - The email-specific translations
 */
export function getEmailTranslations(dictionary: Dictionary): EmailDictionary {
  return dictionary.email;
}

/**
 * Helper function to get email dictionary and extract email translations
 * @param locale - The locale to load (defaults to 'ja')
 * @returns Promise<EmailDictionary> - The email-specific translations
 */
export async function loadEmailTranslations(
  locale = 'ja'
): Promise<EmailDictionary> {
  const dictionary = await getEmailDictionary(locale);
  return getEmailTranslations(dictionary);
}

/**
 * Simple template string replacement function for email translations
 * @param template - The template string with {variable} placeholders
 * @param variables - Object with variable values
 * @returns string - The template with variables replaced
 */
export function interpolateEmailTemplate(
  template: string,
  variables: Record<string, string | number>
): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
}

/**
 * Get localized email subject with variable interpolation
 * @param emailType - The type of email (e.g., 'return_request_auto_approved')
 * @param locale - The locale to use
 * @param variables - Variables for template interpolation
 * @returns Promise<string> - The localized subject
 */
export async function getLocalizedEmailSubject(
  emailType: keyof EmailDictionary,
  locale = 'ja',
  variables: Record<string, string | number> = {}
): Promise<string> {
  const translations = await loadEmailTranslations(locale);
  const emailTranslations = translations[emailType];

  if (!emailTranslations || !('preview' in emailTranslations)) {
    console.error(
      `No preview found for email type "${emailType}" in locale "${locale}"`
    );
    return `Email ${emailType}`;
  }

  return interpolateEmailTemplate(
    emailTranslations.preview as string,
    variables
  );
}

/**
 * Validate if a locale is supported for emails
 * @param locale - The locale to validate
 * @returns boolean - Whether the locale is supported
 */
export function isValidEmailLocale(locale: string): locale is EmailLocale {
  const normalizedLocale = locale.split('-')[0];
  return EMAIL_LOCALES.includes(normalizedLocale as EmailLocale);
}
