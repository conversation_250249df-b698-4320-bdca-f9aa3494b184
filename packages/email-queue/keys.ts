import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    server: {
      UPSTASH_REDIS_REST_URL: z.string().min(1).url().optional(),
      UPSTASH_REDIS_REST_TOKEN: z.string().min(1).optional(),
      // Optional: Redis connection for BullMQ (if using direct Redis instead of REST)
      REDIS_URL: z.string().url().optional(),
      REDIS_PROXY_PORT: z.string().min(1),
      REDIS_PASSWORD: z.string().min(1),
      REDIS_PROXY_HOST: z.string().min(1),
    },
    runtimeEnv: {
      UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
      UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
      REDIS_URL: process.env.REDIS_URL,
      REDIS_PROXY_PORT: process.env.REDIS_PROXY_PORT,
      REDIS_PASSWORD: process.env.REDIS_PASSWORD,
      REDIS_PROXY_HOST: process.env.REDIS_PROXY_HOST,
    },
  });
