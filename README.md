# Senders Return

Welcome to Senders Return! This document provides a comprehensive overview of the project, its structure, and instructions for setting up your local development environment.

## Project Purpose

Senders Return is a comprehensive platform for managing e-commerce returns and exchanges. It provides a seamless experience for both customers and administrators.

-   **For Customers:** A user-friendly portal to initiate and track returns and exchanges.
-   **For Administrators:** A powerful dashboard to manage, process, and analyze return and exchange requests.

## User Flow

### Customer Flow

1.  **Access the Portal:** The customer receives a link to the return portal, where they can look up their order.
2.  **Initiate a Return/Exchange:** The customer selects the items they wish to return or exchange and provides a reason.
3.  **Process the Return:** The customer is guided through the return process, which may include selecting a return method and printing a shipping label.
4.  **Track Status:** The customer can track the status of their return or exchange in the portal and receives email notifications.

### Administrator Flow

1.  **Manage Requests:** Administrators log in to the admin dashboard to view and manage all incoming return and exchange requests.
2.  **Process Returns:** They can use a scanning feature to process incoming packages, update the status of returns, and issue refunds or exchanges.
3.  **Customize and Configure:** Administrators can customize receipt templates, manage user accounts, and configure various settings for the returns process.

## Project Structure

### Project Structure

The project is organized into two main directories:

-   `apps/`: Contains the individual, deployable applications.
-   `packages/`: Contains shared code, components, and configurations used across the different applications.

#### Applications

-   **`api`**: A Next.js application that serves as the backend API for the platform.
-   **`app`**: The main web application for users.
-   **`portal`**: A Next.js application that likely serves as a customer portal or a separate user-facing interface.
-   **`email-worker`**: A dedicated worker for processing and sending emails asynchronously.

#### Key Packages

-   **`@repo/design-system`**: A shared component library based on Radix UI, Tailwind CSS, and shadcn/ui.
-   **`@repo/database`**: Contains the Prisma schema, database client, and migration files.
-   **`@repo/auth`**: Manages authentication logic using `better-auth`.
-   **`@repo/email`**: Handles email templating and sending logic.
-   **`@repo/email-queue`**: An email queue implementation using BullMQ and Redis.
-   ...and many more for handling features like payments, notifications, storage, etc.

## Tech Stack

-   **Framework**: [Next.js](https://nextjs.org/) 15
-   **Language**: [TypeScript](https://www.typescriptlang.org/)
-   **Package Manager**: [pnpm](https://pnpm.io/)
-   **Monorepo Tool**: [Turborepo](https://turbo.build/)
-   **Database ORM**: [Prisma](https://www.prisma.io/)
-   **UI**: [React](https://react.dev/), [Tailwind CSS](https://tailwindcss.com/), [Radix UI](https://www.radix-ui.com/), [shadcn/ui](https://ui.shadcn.com/)
-   **Testing**: [Vitest](https://vitest.dev/)
-   **Linting & Formatting**: [Biome](https://biomejs.dev/)
-   **Email**: [React Email](https://react.email/), [SendGrid](https://sendgrid.com/), [BullMQ](https://bullmq.io/)
-   **Observability**: [Sentry](https://sentry.io/), [Logtail](https://logtail.com/)
-   **Deployment**: [Vercel](https://vercel.com/)

## Prerequisites

Before you begin, ensure you have the following installed on your local machine:

-   [Node.js](https://nodejs.org/) (version 18 or higher)
-   [pnpm](https://pnpm.io/installation) (version 10.8.1 or higher)

## Getting Started

Follow these steps to set up your local development environment:

**1. Clone the Repository**

```bash
git clone <repository-url>
cd senders-return
```

**2. Install Dependencies**

Install all project dependencies using `pnpm`:

```bash
pnpm install
```

This command will install dependencies for all applications and packages in the monorepo.

**3. Set Up Environment Variables**

The project uses environment variables for configuration. You'll find `.env.example` files in the root of each application (e.g., `apps/api/.env.example`, `apps/app/.env.example`).

Create a `.env` file for each application by copying the corresponding `.env.example` file and filling in the required values.

**4. Set Up the Database**

The project uses Prisma for database management. To set up your local database, run the following command to apply the database schema and run migrations:

```bash
pnpm migrate
```

You can also seed the database with initial data using:

```bash
pnpm seed
```

**5. Run the Development Servers**

To start all the development servers (except for the `email-worker`), run:

```bash
pnpm dev
```

This will start the following applications:

-   `api`: [http://localhost:3002](http://localhost:3002)
-   `app`: [http://localhost:3000](http://localhost:3000)
-   `portal`: [http://localhost:3003](http://localhost:3003)

To run a specific application, you can use the `--filter` flag with Turborepo. For example, to run only the `portal` app:

```bash
pnpm portal
```

## Available Scripts

The following are some of the most common scripts available in the root `package.json`:

-   `pnpm build`: Build all applications and packages.
-   `pnpm dev`: Start all development servers.
-   `pnpm lint`: Lint the entire codebase using Biome.
-   `pnpm format`: Format the entire codebase using Biome.
-   `pnpm test`: Run tests for all applications and packages.
-   `pnpm migrate`: Run database migrations.
-   `pnpm seed`: Seed the database.
-   `pnpm clean`: Remove all `node_modules` and `dist` folders.

## Coding Style

We use [Biome](https://biomejs.dev/) for linting and formatting. Please ensure you run the `lint` and `format` scripts before committing your changes.

```bash
pnpm lint
pnpm format
```

## Deployment

The applications in this project are configured for deployment on [Vercel](https://vercel.com/).