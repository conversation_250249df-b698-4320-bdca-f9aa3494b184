import { env } from '@/env';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * Validates the API key from the request headers
 * @param request - The Next.js request object
 * @returns boolean indicating if the API key is valid
 */
export function validateApiKey(request: NextRequest): boolean {
  const apiKey =
    request.headers.get('x-api-key') ||
    request.headers.get('authorization')?.replace('Bearer ', '');

  if (!apiKey) {
    log.warn('API request missing API key', {
      path: request.nextUrl.pathname,
      method: request.method,
      origin: request.headers.get('origin'),
    });
    return false;
  }

  if (apiKey !== env.API_SECRET_KEY) {
    log.warn('API request with invalid API key', {
      path: request.nextUrl.pathname,
      method: request.method,
      origin: request.headers.get('origin'),
      providedKey: `${apiKey.substring(0, 8)}...`,
    });
    return false;
  }

  return true;
}

/**
 * Creates an unauthorized response
 * @returns NextResponse with 401 status
 */
export function createUnauthorizedResponse(): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: 'Unauthorized',
      message:
        'Valid API key required. Provide it via x-api-key header or Authorization: Bearer <key>',
    },
    { status: 401 }
  );
}

/**
 * Higher-order function to wrap API route handlers with authentication
 * @param handler - The original route handler
 * @returns Wrapped handler with authentication
 */
export function withAuth<T = unknown>(
  handler: (
    req: NextRequest,
    context: { params: Promise<T> }
  ) => Promise<NextResponse>
) {
  return (req: NextRequest, context: { params: Promise<T> }) => {
    // Skip authentication for OPTIONS requests (CORS preflight)
    if (req.method === 'OPTIONS') {
      return handler(req, context);
    }

    if (!validateApiKey(req)) {
      return createUnauthorizedResponse();
    }

    log.debug('API request authenticated successfully', {
      path: req.nextUrl.pathname,
      method: req.method,
    });

    return handler(req, context);
  };
}

/**
 * Middleware function to check authentication for API routes
 * @param request - The Next.js request object
 * @returns NextResponse or null to continue
 */
export function authMiddleware(request: NextRequest): NextResponse | null {
  // Skip authentication for health check endpoint
  if (request.nextUrl.pathname === '/health') {
    return null;
  }

  // Skip authentication for OPTIONS requests (CORS preflight)
  if (request.method === 'OPTIONS') {
    return null;
  }

  // Only apply authentication to API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    if (!validateApiKey(request)) {
      return createUnauthorizedResponse();
    }

    log.debug('API middleware: request authenticated', {
      path: request.nextUrl.pathname,
      method: request.method,
    });
  }

  return null;
}
