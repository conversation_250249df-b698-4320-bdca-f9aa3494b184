import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';
import type { EmailDictionary } from '../i18n';
import { interpolateEmailTemplate } from '../i18n';

type ReturnRequestManualReceivedEmailProps = {
  readonly returnNumber: string;
  readonly reviewDays?: string;
  readonly dictionary: EmailDictionary;
};

export const ReturnRequestManualReceivedEmail = ({
  returnNumber,
  reviewDays = '3',
  dictionary,
}: ReturnRequestManualReceivedEmailProps) => {
  const t = dictionary.return_request_manual_received;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 text-zinc-700">
                  {interpolateEmailTemplate(t.review_time, { reviewDays })}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleReturnRequestManualReceivedEmail = () => {
  // Mock dictionary for example
  const mockDictionary = {
    return_request_manual_received: {
      preview: 'Your return request #{returnNumber} is being reviewed',
      title: 'Return Request Received',
      message: 'Your return request #{returnNumber} is being reviewed.',
      review_time:
        'Our customer service team will verify your request and respond within {reviewDays} business days.',
    },
  } as EmailDictionary;

  return (
    <ReturnRequestManualReceivedEmail
      returnNumber="RET-12345"
      reviewDays="3"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleReturnRequestManualReceivedEmail;
