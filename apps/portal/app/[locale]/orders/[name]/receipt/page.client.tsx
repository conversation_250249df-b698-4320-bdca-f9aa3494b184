'use client';

import ReceiptDialog from '@/app/[locale]/orders/[name]/receipt/_components/receipt-dialog';
import type { GetOrdersQuery } from '@/types/admin.generated';
import type { Dictionary } from '@repo/internationalization';

interface ReceiptPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

export default function ReceiptPageClient({
  order,
  dictionary,
}: ReceiptPageClientProps) {
  return (
    <ReceiptDialog
      orderName={order.name}
      order={order}
      dictionary={dictionary}
      inline
    />
  );
}
