'use client';

import type { Dictionary } from '@repo/internationalization';
import {
  type ReturnRequestRow,
  columns,
} from './components/return-request-column';
import { ReturnRequestTable } from './components/return-request-table';

export function ReturnRequestsPageClient({
  returnRequests,
  dictionary,
}: {
  returnRequests: ReturnRequestRow[];
  dictionary: Dictionary;
}) {
  return (
    <ReturnRequestTable
      dictionary={dictionary}
      columns={columns(dictionary)}
      initialData={returnRequests}
    />
  );
}
