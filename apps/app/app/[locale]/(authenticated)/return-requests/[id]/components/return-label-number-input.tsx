'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { Dictionary } from '@repo/internationalization';
import { type FormEvent, type KeyboardEvent, useState } from 'react';
import { isReturnLabelNumberUsed } from '../../actions';

interface ReturnLabelNumberInputProps {
  returnNumber: string;
  currentValue: string;
  dictionary: Dictionary;
  onSubmit: (returnLabelNumber: string) => void;
}

export function ReturnLabelNumberInput({
  returnNumber,
  currentValue,
  dictionary,
  onSubmit,
}: ReturnLabelNumberInputProps) {
  const [inputValue, setInputValue] = useState(currentValue);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if label number is already used
      const isUsed = await isReturnLabelNumberUsed(inputValue.trim());

      if (isUsed) {
        toast.error(dictionary.message.duplicate_label_number, {
          description: dictionary.message.duplicate_label_number_description,
        });
        setIsSubmitting(false);
        return;
      }

      await onSubmit(inputValue.trim());
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      handleSubmit(e);
    }
  };

  return (
    <Card className="border-0 p-0 shadow-none">
      <CardHeader className="p-0">
        <CardTitle>
          {dictionary.admin.scanner.barcode_scanner.return_label_number_title}
        </CardTitle>
        <CardDescription>
          {dictionary.admin.scanner.barcode_scanner.return_label_number_description.replace(
            '{returnNumber}',
            returnNumber
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 p-0">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="returnLabelNumber">
              {
                dictionary.admin.scanner.barcode_scanner
                  .return_label_number_label
              }
            </Label>
            <Input
              id="returnLabelNumber"
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                dictionary.admin.scanner.barcode_scanner
                  .return_label_number_placeholder
              }
              disabled={isSubmitting}
              autoFocus
              className="text-lg"
            />
          </div>
          <Button
            type="submit"
            disabled={!inputValue.trim() || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : null}
            {dictionary.button.continue}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
