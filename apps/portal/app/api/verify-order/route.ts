import { getOrderBasicInfo } from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName, email }: { orderName: string; email: string } =
      await request.json();

    log.info(`Verifying order ${orderName} with email ${email}`);

    if (!orderName || !email) {
      return NextResponse.json(
        { error: 'Order number and email are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Verify the order with email using the lightweight function
    const order = await getOrderBasicInfo(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          verified: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
          key: 'order_not_found_full',
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        verified: true,
        orderName: orderName,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error verifying order:', { error });
    return NextResponse.json(
      { error: 'An error occurred while verifying the order' },
      { status: 500 }
    );
  }
}
