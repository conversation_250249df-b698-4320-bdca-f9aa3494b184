import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type RequestDeclinedEmailProps = {
  readonly returnNumber: string;
  readonly dictionary: EmailDictionary;
};

export const RequestDeclinedEmail = ({
  returnNumber,
  dictionary,
}: RequestDeclinedEmailProps) => {
  const t = dictionary.request_declined;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 mb-4 text-zinc-700">{t.explanation}</Text>
                <Text className="m-0 mb-2 text-zinc-700">
                  {t.reason_customer}
                </Text>
                <Text className="m-0 mb-2 text-zinc-700">{t.reason_time}</Text>
                <Text className="m-0 mb-2 text-zinc-700">
                  {t.reason_damage}
                </Text>
                <Text className="m-0 mb-2 text-zinc-700">
                  {t.reason_missing}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {t.reason_verification}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 text-zinc-700">{t.apology}</Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleRequestDeclinedEmail = () => {
  const mockDictionary = {
    request_declined: {
      preview: 'Your request #{returnNumber} has been declined',
      title: 'Request Declined',
      message: 'Your request #{returnNumber} has been declined.',
      explanation:
        'Your request has been declined. Please note that we cannot accept returns or exchanges in the following cases:',
      reason_customer:
        "• Returns due to customer reasons (size doesn't fit, different from expectations, wrong order, etc.)",
      reason_time:
        '• Products that have been more than 7 days since arrival, or products that have been used once (please return in unopened condition)',
      reason_damage:
        "• Products that have become soiled, scratched, or damaged while in the customer's possession (including damage or scratches from dropping)",
      reason_missing:
        '• Items missing tags, accessories, boxes, or other accompanying items',
      reason_verification:
        "• Cases where we cannot confirm the customer's purchase",
      apology: 'Sorry for any inconvenience.',
    },
  } as EmailDictionary;

  return (
    <RequestDeclinedEmail
      returnNumber="RET-12345"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleRequestDeclinedEmail;
