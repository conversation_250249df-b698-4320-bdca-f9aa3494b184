import type { EmailAttachment } from '../send';

export interface ReturnLabelData {
  orderName: string;
  returnNumber: string;
  // New optional fields to align with PDF API sample payload
  // Example:
  // {
  //   "orderName": "ORD-2024-001",
  //   "returnNumber": "RET-123456789",
  //   "returnDate": "2024-08-03",
  //   "items": [
  //     { "itemName": "Wireless Headphones", "quantity": 1, "sku": "WH-001" },
  //     { "itemName": "Phone Case", "quantity": 2 }
  //   ]
  // }
  returnDate?: string;
  items?: Array<{ itemName: string; quantity: number; sku?: string }>;
}

// TODO: add authentication
export async function generateReturnLabelPDF(
  data: ReturnLabelData
): Promise<EmailAttachment> {
  try {
    const response = await fetch('https://pdf.senders.jp/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to generate PDF: ${response.statusText}`);
    }

    const pdfAttachment: EmailAttachment = await response.json();
    return pdfAttachment;
  } catch (error) {
    console.error('Error generating return label PDF:', error);
    throw new Error('Failed to generate return label PDF');
  }
}
