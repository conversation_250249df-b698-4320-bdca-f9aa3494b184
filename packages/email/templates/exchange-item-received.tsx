import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';
import type { EmailDictionary } from '../i18n';
import { interpolateEmailTemplate } from '../i18n';

type ExchangeItemReceivedEmailProps = {
  readonly returnNumber: string;
  readonly dictionary: EmailDictionary;
};

export const ExchangeItemReceivedEmail = ({
  returnNumber,
  dictionary,
}: ExchangeItemReceivedEmailProps) => {
  const t = dictionary.exchange_item_received;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-4 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 text-zinc-700">{t.completion}</Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleExchangeItemReceivedEmail = () => {
  // Mock dictionary for example
  const mockDictionary = {
    exchange_item_received: {
      preview: 'Exchange #{returnNumber} is now complete',
      title: 'Exchange Complete',
      message:
        "We've received your returned item for exchange #{returnNumber}.",
      completion: 'Your exchange is now complete. Thank you for your business!',
    },
  } as EmailDictionary;

  return (
    <ExchangeItemReceivedEmail
      returnNumber="EXC-12345"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleExchangeItemReceivedEmail;
