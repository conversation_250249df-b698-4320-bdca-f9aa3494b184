'use client';

import type { Dictionary } from '@repo/internationalization';
import {
  type ReceiptCustomizationRow,
  columns,
} from './components/receipt-customization-column';
import { ReceiptCustomizationTable } from './components/receipt-customization-table';

export function ReceiptCustomizationsPageClient({
  receiptCustomizations,
  dictionary,
}: {
  receiptCustomizations: ReceiptCustomizationRow[];
  dictionary: Dictionary;
}) {
  return (
    <ReceiptCustomizationTable
      dictionary={dictionary}
      columns={columns(dictionary)}
      initialData={receiptCustomizations}
    />
  );
}
