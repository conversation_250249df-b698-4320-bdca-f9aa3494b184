import { prisma } from '@/lib/prisma';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const orderName = searchParams.get('orderName');
    const lineItemIds = searchParams.getAll('lineItemId');

    if (!orderName) {
      return NextResponse.json(
        { error: 'Order name is required' },
        { status: 400 }
      );
    }

    // Get all return requests with their items for this order
    const existingReturns = await prisma.returnRequest.findMany({
      where: {
        orderName,
        returnItems:
          lineItemIds.length > 0
            ? { some: { lineItemId: { in: lineItemIds } } }
            : undefined,
      },
      include: {
        returnItems: true,
      },
    });

    // Transform the data to match the expected format
    const returnedItems = existingReturns.flatMap((request) =>
      request.returnItems.map((item) => ({
        id: item.id,
        orderName: request.orderName,
        lineItemId: item.lineItemId,
        returnReason: request.returnReason,
        status: request.status,
        returnLabelOption: request.returnLabelOption,
        returnNumber: request.returnNumber,
        title: item.title,
        variantTitle: item.variantTitle,
        sku: item.sku,
        barcode: item.barcode,
      }))
    );

    return NextResponse.json({
      success: true,
      existingReturns: returnedItems,
    });
  } catch (error) {
    log.error('Error checking existing returns:', { error });
    return NextResponse.json(
      { error: 'An error occurred while checking existing returns' },
      { status: 500 }
    );
  }
}
