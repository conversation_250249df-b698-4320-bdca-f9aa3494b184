'use client';

import { updateAddressChangeRequest } from '@/app/[locale]/(authenticated)/address-changes/actions';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import type { Dictionary } from '@repo/internationalization';
import { useState } from 'react';
import { z } from 'zod';

const formSchema = z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'completed']),
  adminNotes: z.string().optional(),
});

type AddressChangeStatusFormProps = {
  id: string;
  currentStatus: string;
  dictionary: Dictionary;
};

export function AddressChangeStatusForm({
  id,
  currentStatus,
  dictionary,
}: AddressChangeStatusFormProps) {
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: currentStatus as
        | 'pending'
        | 'approved'
        | 'rejected'
        | 'completed',
      adminNotes: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await updateAddressChangeRequest(id, values);
      toast.success(dictionary.admin.address_changes.status_updated);
      setOpen(false);
    } catch (error) {
      console.error('Failed to update address change request:', error);
      toast.error(dictionary.admin.address_changes.status_update_failed);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          {dictionary.admin.address_changes.update_status}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {dictionary.admin.address_changes.update_address_change_status}
          </DialogTitle>
          <DialogDescription>
            {dictionary.admin.address_changes.update_status_description}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>
                    {dictionary.admin.address_changes.status}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            dictionary.admin.address_changes.select_status ||
                            'Select a status'
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">
                        {dictionary.admin.address_changes.status_pending}
                      </SelectItem>
                      <SelectItem value="approved">
                        {dictionary.admin.address_changes.status_approved}
                      </SelectItem>
                      <SelectItem value="rejected">
                        {dictionary.admin.address_changes.status_rejected ||
                          'Rejected'}
                      </SelectItem>
                      <SelectItem value="completed">
                        {dictionary.admin.address_changes.status_completed ||
                          'Completed'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {dictionary.admin.address_changes.status_description}
                  </FormDescription>
                  <FormMessage />
                </GridFormItem>
              )}
            />
            <FormField
              control={form.control}
              name="adminNotes"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>
                    {dictionary.admin.address_changes.admin_notes}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={
                        dictionary.admin.address_changes.admin_notes_placeholder
                      }
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {dictionary.admin.address_changes.admin_notes_description}
                  </FormDescription>
                  <FormMessage />
                </GridFormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">
                {dictionary.admin.address_changes.save_changes}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
