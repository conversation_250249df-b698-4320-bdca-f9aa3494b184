import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';

type SendersShareEmailProps = {
  readonly shareId?: string;
  readonly senderEmail?: string;
  readonly title?: string;
  readonly message?: string;
  readonly fileCount?: number;
  readonly expiresAt?: string;
  readonly isPrivate?: boolean;
  readonly dictionary: EmailDictionary;
};

export const SendersShareEmail = ({
  shareId,
  senderEmail,
  title,
  message,
  fileCount,
  expiresAt,
  isPrivate,
  dictionary,
}: SendersShareEmailProps) => {
  const t = dictionary.senders_share;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {title
            ? isPrivate
              ? interpolateEmailTemplate(t.preview_private, { title })
              : interpolateEmailTemplate(t.preview, { title })
            : isPrivate
              ? t.preview_no_title_private
              : t.preview_no_title}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://www.senders.jp/logo.png"
              alt="Senders Logo"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {title
                    ? interpolateEmailTemplate(t.title, { title })
                    : t.title_default}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {senderEmail
                    ? interpolateEmailTemplate(t.message_from, { senderEmail })
                    : t.message_default}
                </Text>
                {message && (
                  <>
                    <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                      {t.message_label}
                    </Text>
                    <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">
                      {message}
                    </Text>
                  </>
                )}
                <Hr className="my-4" />
                <Text className="m-0 mb-2 text-zinc-700">
                  <strong>{t.file_count}</strong> {fileCount ?? 'N/A'}
                </Text>
                {expiresAt && (
                  <Text className="m-0 mb-2 text-zinc-700">
                    <strong>{t.expires}</strong> {expiresAt}
                  </Text>
                )}
                <Text className="m-0 mb-2 text-zinc-700">
                  <strong>{t.privacy}</strong>{' '}
                  {isPrivate ? t.private : t.public}
                </Text>
                {shareId && (
                  <Text className="m-0 mt-4 text-blue-600 underline">
                    <a href={`https://yourdomain.com/share/${shareId}`}>
                      {t.download_button}
                    </a>
                  </Text>
                )}
                <Hr className="my-4" />
                <Text className="m-0 text-sm text-zinc-500">
                  {t.automatic_notice}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleSendersShareEmail = () => {
  const mockDictionary = {
    senders_share: {
      preview: '{title} - You have received a new file share',
      preview_no_title: 'You have received a new file share',
      preview_no_title_private: 'You have received a new file share (Private)',
      preview_private: '{title} - You have received a new file share (Private)',
      title: '{title}',
      title_default: 'You have received a new file share',
      message_from: 'From: {senderEmail}',
      message_default: 'A sender has shared files with you.',
      message_label: 'Message:',
      share_id: 'Share ID:',
      file_count: 'Files:',
      expires: 'Expires:',
      privacy: 'Privacy:',
      private: 'Private',
      public: 'Public',
      download_button: 'Download Files',
      automatic_notice:
        'This email was sent automatically. If you have any questions, please contact support.',
    },
  } as EmailDictionary;

  return (
    <SendersShareEmail
      shareId="SHARE-12345"
      senderEmail="<EMAIL>"
      title="Project Files"
      message="Here are the files for the project."
      fileCount={3}
      expiresAt="2024-07-01"
      isPrivate={true}
      dictionary={mockDictionary}
    />
  );
};

export default ExampleSendersShareEmail;
