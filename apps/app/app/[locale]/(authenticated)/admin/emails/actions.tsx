'use server';

import { env } from '@/env';
import { log } from '@repo/observability/log';

export async function retryJobAction(jobId: string) {
  log.info(`Retrying job: ${jobId}`);

  const response = await fetch(
    `${env.NEXT_PUBLIC_API_URL}/api/email-queue/manage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${env.API_SECRET_KEY}`,
      },
      body: JSON.stringify({ action: 'retry', jobId }),
    }
  );

  return response;
}

export async function handleQueueAction(action: string, jobId?: string) {
  log.info(`Handling queue action: ${action}`);

  const response = await fetch(
    `${env.NEXT_PUBLIC_API_URL}/api/email-queue/manage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${env.API_SECRET_KEY}`,
      },
      body: JSON.stringify({ action, jobId }),
    }
  );

  return response;
}
