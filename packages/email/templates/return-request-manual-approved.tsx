import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type ReturnRequestManualApprovedEmailProps = {
  readonly returnNumber: string;
  readonly refundDays?: string;
  readonly returnInstructions?: string;
  readonly dictionary: EmailDictionary;
};

export const ReturnRequestManualApprovedEmail = ({
  returnNumber,
  refundDays = '5-7',
  returnInstructions,
  dictionary,
}: ReturnRequestManualApprovedEmailProps) => {
  const t = dictionary.return_request_manual_approved;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 mb-4 text-zinc-700">
                  <strong>{t.next_steps}</strong> {t.next_steps_text}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  <strong>{t.refund}</strong>{' '}
                  {interpolateEmailTemplate(t.refund_text, { refundDays })}
                </Text>
                {returnInstructions && (
                  <>
                    <Hr className="my-4" />
                    <Text className="m-0 text-zinc-700">
                      <strong>{t.instructions}</strong>
                    </Text>
                    <Text className="m-0 text-zinc-700">
                      {returnInstructions}
                    </Text>
                  </>
                )}
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleReturnRequestManualApprovedEmail = () => {
  const mockDictionary = {
    return_request_manual_approved: {
      preview: 'Your return request #{returnNumber} has been approved',
      title: 'Return Request Approved',
      message: 'Your return request #{returnNumber} has been approved.',
      next_steps: 'Next steps:',
      next_steps_text:
        'Please follow the return shipping instructions or use the prepaid label provided.',
      refund: 'Refund:',
      refund_text:
        "You'll receive your refund within {refundDays} business days once we receive your item.",
      instructions: 'Return Instructions:',
    },
  } as EmailDictionary;

  return (
    <ReturnRequestManualApprovedEmail
      returnNumber="RET-12345"
      refundDays="5-7"
      returnInstructions="Please package the item securely and use the provided prepaid shipping label."
      dictionary={mockDictionary}
    />
  );
};

export default ExampleReturnRequestManualApprovedEmail;
