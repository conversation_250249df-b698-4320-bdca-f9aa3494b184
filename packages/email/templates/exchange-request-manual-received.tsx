import {
  Body,
  Container,
  Head,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type ExchangeRequestManualReceivedEmailProps = {
  readonly returnNumber: string;
  readonly dictionary: EmailDictionary;
};

export const ExchangeRequestManualReceivedEmail = ({
  returnNumber,
  dictionary,
}: ExchangeRequestManualReceivedEmailProps) => {
  const t = dictionary.exchange_request_manual_received;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleExchangeRequestManualReceivedEmail = () => {
  const mockDictionary = {
    exchange_request_manual_received: {
      preview: 'Your exchange request #{returnNumber} is being reviewed',
      title: 'Exchange Request Received',
      message: 'Your exchange request #{returnNumber} is being reviewed.',
    },
  } as EmailDictionary;

  return (
    <ExchangeRequestManualReceivedEmail
      returnNumber="EXC-12345"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleExchangeRequestManualReceivedEmail;
