'use client';

import * as React from 'react';
import { cn } from '@repo/design-system/lib/utils';

interface SegmentedControlProps {
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const SegmentedControl = React.forwardRef<
  HTMLDivElement,
  SegmentedControlProps
>(({ options, value, onChange, className }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'grid w-full grid-cols-2 gap-2 rounded-lg bg-muted p-1 text-muted-foreground',
        className
      )}
    >
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() => onChange(option.value)}
          className={cn(
            'inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
            {
              'bg-background text-foreground shadow-sm': value === option.value,
            }
          )}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
});

SegmentedControl.displayName = 'SegmentedControl';

export { SegmentedControl };
