'use client';

import type { ReturnRequestWithItems } from '@/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { CheckCircle, Volume2, VolumeX } from 'lucide-react';
import { title } from 'radash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { BarcodeScanner } from '../return-requests/[id]/barcode-scanner';
import { updateReturnRequest } from '../return-requests/actions';
import { findReturnByReturnNumber } from './action';

export function ReturnScannerClient({
  dictionary,
}: { dictionary: Dictionary }) {
  const [trackingNumber, setTrackingNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [foundRequest, setFoundRequest] =
    useState<ReturnRequestWithItems | null>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioEnabled, setAudioEnabled] = useState(true);
  const trackingInputRef = useRef<HTMLInputElement>(null);

  // Audio feedback
  const errorAudio = useRef<HTMLAudioElement>(null);
  const successAudio = useRef<HTMLAudioElement>(null);

  // Initialize audio elements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      errorAudio.current = new Audio('/audio/error.mp3');
      successAudio.current = new Audio('/audio/success.mp3');
    }
  }, []);

  const playAudio = useCallback(
    (isSuccess: boolean) => {
      if (!audioEnabled) {
        return;
      }

      try {
        if (isSuccess && successAudio.current) {
          successAudio.current.play();
        } else if (!isSuccess && errorAudio.current) {
          errorAudio.current.play();
        }
      } catch {
        // Audio playback failed - this is not critical
      }
    },
    [audioEnabled]
  );

  const searchByTrackingNumber = useCallback(
    async (tracking: string, loading = false) => {
      if (!tracking.trim()) {
        return;
      }

      setErrorMessage('');

      if (loading) {
        setIsLoading(true);
        setFoundRequest(null);
      }

      try {
        const returnRequest = await findReturnByReturnNumber(tracking.trim());

        if (returnRequest) {
          setFoundRequest(returnRequest);
          playAudio(true);

          toast.success(
            dictionary.admin.scanner.return_scanner.toast_found_title,
            {
              description:
                dictionary.admin.scanner.return_scanner.toast_found_description.replace(
                  '{returnNumber}',
                  returnRequest.returnNumber
                ),
            }
          );
        } else {
          setErrorMessage(dictionary.admin.scanner.return_scanner.not_found);
          playAudio(false);

          toast.error(
            dictionary.admin.scanner.return_scanner.toast_not_found_title,
            {
              description: dictionary.admin.scanner.return_scanner.not_found,
            }
          );
        }
      } catch (error) {
        console.error('Error searching for return request:', error);
        setErrorMessage(dictionary.admin.scanner.return_scanner.error);
        playAudio(false);
        toast.error(dictionary.admin.scanner.return_scanner.toast_error_title, {
          description: dictionary.admin.scanner.return_scanner.error,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [playAudio, dictionary]
  );

  const handleTrackingInput = useCallback(
    (value: string) => {
      setTrackingNumber(value);
      // Auto-search when tracking number looks complete (adjust length as needed)
      if (value.trim().length >= 4) {
        searchByTrackingNumber(value, true);
      }
    },
    [searchByTrackingNumber]
  );

  const handleSubmit = () => {
    if (trackingNumber.trim()) {
      searchByTrackingNumber(trackingNumber.trim(), true);
    }
  };

  const handleProcessRequest = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    if (!foundRequest) {
      return;
    }

    log.info('All items verified:', scannedItems);

    // Determine the next processed status based on exchange type and current status
    let newProcessedStatus = 'completed';

    if (
      foundRequest.exchangeType === 'return' &&
      foundRequest.processed === 'pending'
    ) {
      newProcessedStatus = 'completed';
    }

    log.info(`Updating return request status to: ${newProcessedStatus}`);

    await updateReturnRequest(foundRequest.id, {
      processed: newProcessedStatus,
    });

    searchByTrackingNumber(trackingNumber.trim());
  };

  const handleReturnLabelNumber = async (returnLabelNumber: string) => {
    if (!foundRequest) {
      return;
    }

    log.info(`Return label number entered: ${returnLabelNumber}`);

    await updateReturnRequest(foundRequest.id, {
      returnLabelNumber,
    });
  };

  return (
    <div className="space-y-6 py-4">
      <Card>
        <CardHeader className="flex items-center gap-2">
          <div>
            <CardTitle>
              {dictionary.admin.scanner.return_scanner.title}
            </CardTitle>
            <CardDescription>
              {dictionary.admin.scanner.return_scanner.description}
            </CardDescription>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="ml-auto flex items-center gap-2"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
            {audioEnabled
              ? dictionary.admin.scanner.return_scanner.audio_on
              : dictionary.admin.scanner.return_scanner.audio_off}
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tracking-input" className="font-medium text-sm">
              {dictionary.admin.scanner.return_scanner.return_number}
            </Label>
            <div className="flex gap-2">
              <Input
                ref={trackingInputRef}
                id="tracking-input"
                type="text"
                value={trackingNumber}
                onChange={(e) => handleTrackingInput(e.target.value)}
                placeholder={
                  dictionary.admin.scanner.return_scanner.placeholder
                }
                className="flex-1"
                autoFocus
              />
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !trackingNumber.trim()}
              >
                {isLoading
                  ? dictionary.admin.scanner.return_scanner.searching
                  : dictionary.admin.scanner.return_scanner.search}
              </Button>
            </div>
          </div>

          {errorMessage && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3 text-red-800">
              {errorMessage}
            </div>
          )}
        </CardContent>
      </Card>

      {foundRequest && (
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">
              {dictionary.admin.scanner.return_scanner.found_title}
            </CardTitle>
            <CardDescription>
              {dictionary.admin.scanner.return_scanner.found_description
                .replace('{returnNumber}', foundRequest.returnNumber)
                .replace('{status}', title(foundRequest.processed))}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>{dictionary.admin.scanner.common.order}:</strong>{' '}
                {foundRequest.orderName}
              </div>
              <div>
                <strong>{dictionary.admin.scanner.common.email}:</strong>{' '}
                {foundRequest.email}
              </div>
              <div>
                <strong>{dictionary.admin.scanner.common.type}:</strong>{' '}
                {title(foundRequest.exchangeType)}
              </div>
              <div>
                <strong>{dictionary.admin.scanner.common.items}:</strong>{' '}
                {foundRequest.returnItems.length}
              </div>
              <div>
                <strong>
                  {dictionary.admin.scanner.common.return_label_number}:
                </strong>{' '}
                {foundRequest.returnLabelNumber || '-'}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">
                {dictionary.admin.scanner.common.return_items}:
              </h4>
              <ul className="space-y-1 text-sm">
                {foundRequest.returnItems.map((item) => (
                  <li key={item.id} className="flex justify-between">
                    <span>{item.title}</span>
                    <span>
                      {dictionary.admin.scanner.common.qty}: {item.quantity}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {foundRequest.processed === 'completed' ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {foundRequest.exchangeType === 'exchange'
                      ? dictionary.admin.scanner.common.exchange_processing
                      : dictionary.admin.scanner.common.return_processing}
                  </CardTitle>
                  <CardDescription className="mt-2 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    {foundRequest.exchangeType === 'exchange'
                      ? dictionary.admin.scanner.common.exchange_completed
                      : dictionary.admin.scanner.common.return_completed}
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <BarcodeScanner
                request={foundRequest}
                skipReturnPackageScan
                dictionary={dictionary}
                onSubmit={handleProcessRequest}
                onReturnLabelNumberSubmit={handleReturnLabelNumber}
              />
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
