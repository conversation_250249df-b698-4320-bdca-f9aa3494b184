import { env } from '@/env';
import { log } from '@repo/observability/log';

// Japan Post API types based on the OpenAPI specification
interface JapanPostTokenRequest {
  grant_type: 'client_credentials';
  client_id: string;
  secret_key: string;
}

interface JapanPostTokenResponse {
  token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

interface JapanPostAddress {
  dgacode: string | null;
  zip_code: string;
  pref_code: string;
  pref_name: string;
  pref_kana: string | null;
  pref_roma: string | null;
  city_code: string;
  city_name: string;
  city_kana: string | null;
  city_roma: string | null;
  town_name: string;
  town_kana: string | null;
  town_roma: string | null;
  biz_name: string | null;
  biz_kana: string | null;
  biz_roma: string | null;
  block_name: string | null;
  other_name: string | null;
  address: string | null;
  longitude: string | null;
  latitude: string | null;
}

interface JapanPostSearchResponse {
  addresses: JapanPostAddress[];
  searchtype: string;
  limit: number;
  count: number;
  page: number;
}

interface JapanPostErrorResponse {
  request_id: string;
  error_code: string;
  message: string;
}

// Token cache to avoid frequent token requests
let cachedToken: {
  access_token: string;
  expires_at: number;
} | null = null;

const JAPAN_POST_BASE_URL = 'https://stub-qz73x.da.pf.japanpost.jp';

/**
 * Get a valid access token for Japan Post API
 * Uses cached token if still valid, otherwise requests a new one
 */
async function getAccessToken(): Promise<string> {
  // Check if we have a valid cached token
  if (cachedToken && Date.now() < cachedToken.expires_at) {
    log.info('Using cached Japan Post API token', {
      token: cachedToken.access_token,
      expiresAt: cachedToken.expires_at,
      timeRemaining: cachedToken.expires_at - Date.now(),
    });

    return cachedToken.access_token;
  }

  try {
    log.info('Requesting new Japan Post API token');

    const tokenRequest: JapanPostTokenRequest = {
      grant_type: 'client_credentials',
      client_id: env.JAPAN_POST_CLIENT_ID,
      secret_key: env.JAPAN_POST_CLIENT_SECRET,
    };

    const response = await fetch(`${JAPAN_POST_BASE_URL}/api/v1/j/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-forwarded-for': '127.0.0.1', // Required header
      },
      body: JSON.stringify(tokenRequest),
    });

    if (!response.ok) {
      const errorData = (await response.json()) as JapanPostErrorResponse;
      log.error('Failed to get Japan Post API token:', {
        status: response.status,
        error: errorData,
      });
      throw new Error(`Token request failed: ${errorData.message}`);
    }

    const tokenData = (await response.json()) as JapanPostTokenResponse;

    // Cache the token with expiration time (subtract 60 seconds for safety)
    cachedToken = {
      access_token: tokenData.token,
      expires_at: Date.now() + (tokenData.expires_in - 60) * 1000,
    };

    log.info('Successfully obtained Japan Post API token', { tokenData });
    return tokenData.token;
  } catch (error) {
    log.error('Error getting Japan Post API token:', { error });
    throw error;
  }
}

/**
 * Search for addresses by postal code using Japan Post API
 * @param postalCode - Japanese postal code (e.g., "1000001" or "100-0001")
 * @returns Array of matching addresses
 */
export async function searchAddressByPostalCode(
  postalCode: string
): Promise<JapanPostAddress[]> {
  try {
    // Clean the postal code (remove hyphens and spaces)
    const cleanPostalCode = postalCode.replace(/[-\s]/g, '');

    // Validate postal code format (7 digits)
    if (!/^\d{7}$/.test(cleanPostalCode)) {
      throw new Error('Invalid postal code format. Expected 7 digits.');
    }

    log.info('Searching address by postal code:', {
      postalCode: cleanPostalCode,
    });

    const accessToken = await getAccessToken();

    log.info('accessToken', { accessToken });

    const response = await fetch(
      `${JAPAN_POST_BASE_URL}/api/v1/searchcode/${cleanPostalCode}?limit=10`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = (await response.json()) as JapanPostErrorResponse;
      log.error('Failed to search address by postal code:', {
        status: response.status,
        error: errorData,
        postalCode: cleanPostalCode,
      });

      if (response.status === 404) {
        // No addresses found for this postal code
        return [];
      }

      throw new Error(`Address search failed: ${errorData.message}`);
    }

    const searchData = (await response.json()) as JapanPostSearchResponse;

    log.info('Successfully found addresses:', {
      postalCode: cleanPostalCode,
      count: searchData.count,
    });

    return searchData.addresses;
  } catch (error) {
    log.error('Error searching address by postal code:', {
      error,
      postalCode,
    });
    throw error;
  }
}

/**
 * Format a Japan Post address for use in forms
 * @param address - Japan Post address object
 * @returns Formatted address object suitable for form population
 */
export function formatJapanPostAddress(address: JapanPostAddress) {
  return {
    prefecture: address.pref_name,
    city: address.city_name,
    town: address.town_name,
    postalCode: address.zip_code,
    // Combine town and block for address line 1
    address1: [address.town_name, address.block_name].filter(Boolean).join(' '),
    // Use business name or other name for address line 2 if available
    address2: address.biz_name || address.other_name || '',
    // Full formatted address for display
    fullAddress: [
      address.pref_name,
      address.city_name,
      address.town_name,
      address.block_name,
    ]
      .filter(Boolean)
      .join(' '),
  };
}

/**
 * Check if Japan Post API is available for the current environment
 * @returns true if Japan Post API should be used
 */
export function isJapanPostAvailable(): boolean {
  return env.NEXT_PUBLIC_REGION === 'JP';
}
