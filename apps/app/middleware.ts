import { env } from '@/env';
import { authMiddleware } from '@repo/auth/middleware';
import { internationalizationMiddleware } from '@repo/internationalization/middleware';
import {
  noseconeMiddleware,
  noseconeOptions,
  noseconeOptionsWithToolbar,
} from '@repo/security/middleware';
import type { NextFetchEvent, NextMiddleware, NextRequest } from 'next/server';

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest|mp3)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};

// Security headers middleware
const securityHeaders = env.FLAGS_SECRET
  ? noseconeMiddleware(noseconeOptionsWithToolbar)
  : noseconeMiddleware(noseconeOptions);

// Chain the middleware: auth -> i18n -> security headers
const middleware = (request: NextRequest, event: NextFetchEvent) => {
  // Skip internationalization for API routes
  if (request.nextUrl.pathname.startsWith('/api')) {
    return authMiddleware(() => securityHeaders())(request, event);
  }

  // For non-API routes, apply auth, then i18n, then security headers
  return authMiddleware((req) => {
    const i18nResponse = internationalizationMiddleware(req);
    if (i18nResponse) {
      return i18nResponse;
    }
    return securityHeaders();
  })(request, event);
};

export default middleware as unknown as NextMiddleware;
