'use client';

import { Card, CardContent } from '@repo/design-system/components/ui/card';
import { CustomRadio } from '@repo/design-system/components/ui/custom-radio';
import { Label } from '@repo/design-system/components/ui/label';
import { MultiPhotoUploader } from '@repo/design-system/components/ui/multi-photo-uploader';
import { SegmentedControl } from '@repo/design-system/components/ui/segmented-control';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { formatCurrency } from '@repo/design-system/lib/format';
import SelectedItemsDisplay from '../selected-items-display';
import type { DefectType, StepProps } from '../types';

export default function Step3ReturnDetails({
  flowState,
  updateFlowState,
  selectedItems,
  dictionary,
  totalAmount,
}: StepProps) {
  const handleExchangeTypeSelect = (type: 'exchange' | 'return') => {
    updateFlowState({ exchangeType: type });
  };

  const exchangeOptions = [
    {
      value: 'exchange',
      label: dictionary.return.exchange_replacement,
    },
    {
      value: 'return',
      label: dictionary.return.return_refund,
    },
  ];

  // For defect and wrong_product, show the segmented control
  if (
    flowState.returnReason === 'defect' ||
    flowState.returnReason === 'wrong_product'
  ) {
    return (
      <div className="space-y-4">
        <SelectedItemsDisplay
          selectedItems={selectedItems}
          dictionary={dictionary}
        />
        <div>
          <h3 className="font-medium text-lg">
            {flowState.returnReason === 'defect'
              ? dictionary.return.defective_options
              : dictionary.return.wrong_product_options}
          </h3>

          <p className="mb-4 text-muted-foreground text-sm">
            {dictionary.return.exchange_question}
          </p>
        </div>

        <SegmentedControl
          options={exchangeOptions}
          value={flowState.exchangeType ?? exchangeOptions[0].value}
          onChange={(value) =>
            handleExchangeTypeSelect(value as 'exchange' | 'return')
          }
        />

        {flowState.returnReason === 'defect' && (
          <div className="mt-4 space-y-4">
            <div className="overflow-hidden rounded-md shadow-xs">
              {[
                { value: 'broken', label: dictionary.return.defect.broken },
                {
                  value: 'scratched',
                  label: dictionary.return.defect.scratched,
                },
                { value: 'dirt', label: dictionary.return.defect.dirt },
                { value: 'other', label: dictionary.return.defect.other },
              ].map((item) => (
                <CustomRadio
                  key={`defect-${item.value}`}
                  id={`defect-${item.value}`}
                  label={item.label}
                  checked={flowState.defectType === item.value}
                  onCheckedChange={() =>
                    updateFlowState({ defectType: item.value as DefectType })
                  }
                  name="defectType"
                  value={item.value}
                />
              ))}
            </div>
            {flowState.defectType && (
              <>
                <div className="mt-4 space-y-2">
                  <Label htmlFor="defect-description">
                    {flowState.defectType === 'other'
                      ? dictionary.return.defect.other_reason_prompt
                      : dictionary.return.defect.details_prompt}
                  </Label>
                  <Textarea
                    id="defect-description"
                    value={flowState.defectDetails || ''}
                    onChange={(e) =>
                      updateFlowState({ defectDetails: e.target.value })
                    }
                    className="min-h-[100px]"
                  />
                  <p className="text-muted-foreground text-xs">
                    {(flowState.defectDetails || '').trim().split(/\s+/).length}
                    /30 words
                  </p>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex items-center gap-1">
                    <Label htmlFor="defect-photo">
                      {dictionary.return.defect.photo_prompt}
                    </Label>
                    <span className="text-destructive">*</span>
                    <span className="ml-1 text-muted-foreground text-xs">
                      {dictionary.common.required}
                    </span>
                  </div>

                  <MultiPhotoUploader
                    initialPhotos={flowState.defectPhotos}
                    onPhotosChange={(photos) =>
                      updateFlowState({ defectPhotos: photos })
                    }
                    maxPhotos={3}
                    maxSizeMB={5}
                    dictionary={dictionary}
                    access="private"
                  />

                  {flowState.defectPhotos &&
                    flowState.defectPhotos.length === 0 && (
                      <p className="mt-1 text-destructive text-xs">
                        {dictionary.return.defect.photo_required}
                      </p>
                    )}
                </div>
              </>
            )}
          </div>
        )}
      </div>
    );
  }

  // For other reasons, show the standard return details
  return (
    <div className="space-y-4">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      <h3 className="font-medium text-lg">
        {dictionary.return.return_details}
      </h3>

      <Card className="overflow-hidden">
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.reason_label}
              </span>
              <span className="font-medium">
                {flowState.returnReason &&
                  dictionary.return.reason[flowState.returnReason]}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.total_items}
              </span>
              <span className="font-medium">{selectedItems.length}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.total_amount}
              </span>
              <span className="font-medium">
                {formatCurrency(
                  totalAmount.toString(),
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode === 'JPY'
                    ? 'ja-JP'
                    : 'en-US',
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode
                )}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
