'use client';

import {
  Bread<PERSON>rumb,
  Breadcrumb<PERSON><PERSON>,
  <PERSON>read<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@repo/design-system/components/ui/breadcrumb';
import { Separator } from '@repo/design-system/components/ui/separator';
import { SidebarTrigger } from '@repo/design-system/components/ui/sidebar';
import { locales } from '@repo/internationalization/index.client';
import { usePathname } from 'next/navigation';
import { isEmpty } from 'radash';
import { Fragment, type ReactNode } from 'react';

type HeaderProps = {
  pages?: string[];
  page: string;
  children?: ReactNode;
};

export const Header = ({ pages, page, children }: HeaderProps) => {
  const pathname = usePathname();
  const pathSegments = pathname.split('/').filter(Boolean);

  let basePath = '';
  let actualPathSegments: string[] = [];
  const potentialLocale = pathSegments[0];

  if (locales.includes(potentialLocale as 'en' | 'ja')) {
    basePath = `/${potentialLocale}`;
    actualPathSegments = pathSegments.slice(1);
  } else {
    basePath = '';
    actualPathSegments = pathSegments;
  }

  return (
    <header className="flex h-16 shrink-0 items-center justify-between gap-2">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {pages?.map((p, index) => {
              const subpath = actualPathSegments
                .slice(0, index + 1)
                .join('/');
              const href = subpath ? `${basePath}/${subpath}` : basePath;

              return (
                <Fragment key={p}>
                  {index > 0 && (
                    <BreadcrumbSeparator className="hidden md:block" />
                  )}
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href={href}>{p}</BreadcrumbLink>
                  </BreadcrumbItem>
                </Fragment>
              );
            })}
            {!isEmpty(pages) && (
              <BreadcrumbSeparator className="hidden md:block" />
            )}
            <BreadcrumbItem>
              <BreadcrumbPage>{page}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      {children}
    </header>
  );
};
