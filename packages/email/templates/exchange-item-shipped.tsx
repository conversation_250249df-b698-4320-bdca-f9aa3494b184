import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';
import { type EmailDictionary, interpolateEmailTemplate } from '../i18n';

type ExchangeItemShippedEmailProps = {
  readonly returnNumber: string;
  readonly trackingNumber: string;
  readonly dictionary: EmailDictionary;
};

export const ExchangeItemShippedEmail = ({
  returnNumber,
  trackingNumber,
  dictionary,
}: ExchangeItemShippedEmailProps) => {
  const t = dictionary.exchange_item_shipped;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Hr className="my-4" />
                <Text className="m-0 mb-4 text-zinc-700">
                  <strong>{t.tracking}</strong> {trackingNumber}
                </Text>
                <Text className="m-0 text-zinc-700">
                  {t.return_instruction}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleExchangeItemShippedEmail = () => {
  const mockDictionary = {
    exchange_item_shipped: {
      preview: 'Your replacement item for request #{returnNumber} has shipped',
      title: 'Your Replacement Item Has Shipped',
      message:
        'Great news! Your replacement item for exchange request #{returnNumber} has been shipped.',
      tracking: 'Tracking Information:',
      return_instruction:
        'Please use the enclosed prepaid label to return your original item.',
    },
  } as EmailDictionary;

  return (
    <ExchangeItemShippedEmail
      returnNumber="EXC-12345"
      trackingNumber="1Z999AA1234567890"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleExchangeItemShippedEmail;
