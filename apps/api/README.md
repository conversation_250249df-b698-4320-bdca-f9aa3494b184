# API Authentication

This API app is protected with simple API key authentication to prevent unauthorized access.

## Setup

1. **Environment Variable**: Set the `API_SECRET_KEY` environment variable in your `.env` file:
   ```bash
   API_SECRET_KEY="your-secret-api-key-here"
   ```

2. **Generate a Secure API Key**: Use a strong, random string for the API key. You can generate one using:
   ```bash
   # Using openssl
   openssl rand -hex 32
   
   # Using Node.js
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   ```

## Usage

All API endpoints (except `/health`) require authentication. Include the API key in your requests using one of these methods:

### Method 1: x-api-key Header
```bash
curl -H "x-api-key: your-secret-api-key-here" \
     -H "Content-Type: application/json" \
     -d '{"type": "return-request-auto-approved", "to": "<EMAIL>", "returnNumber": "RET-123"}' \
     http://localhost:3002/api/email/send
```

### Method 2: Authorization Bearer Token
```bash
curl -H "Authorization: Bearer your-secret-api-key-here" \
     -H "Content-Type: application/json" \
     -d '{"type": "return-request-auto-approved", "to": "<EMAIL>", "returnNumber": "RET-123"}' \
     http://localhost:3002/api/email/send
```

## Protected Endpoints

- `POST /api/email/send` - Send emails
- `POST /api/email-queue/add` - Add emails to queue
- `GET /api/email-queue/stats` - Get queue statistics
- `POST /api/email-queue/manage` - Manage email queue
- `GET /api/email-queue/manage` - Get job details
- `GET /api/email/test` - Test email rendering

## Unprotected Endpoints

- `GET /health` - Health check endpoint

## Error Responses

If authentication fails, you'll receive a 401 Unauthorized response:

```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Valid API key required. Provide it via x-api-key header or Authorization: Bearer <key>"
}
```

## CORS Configuration

The API includes CORS configuration that allows requests from:
- `http://localhost:3000` (main app)
- `http://localhost:3001` (web app)
- `http://localhost:3002` (api app)
- `http://127.0.0.1:3000`
- `http://127.0.0.1:3001`
- `http://127.0.0.1:3002`
- Values from `NEXT_PUBLIC_APP_URL` and `NEXT_PUBLIC_WEB_URL` environment variables

## Security Features

1. **API Key Validation**: All requests are validated against the configured API key
2. **Request Logging**: Authentication attempts are logged for monitoring
3. **CORS Protection**: Only allowed origins can make requests
4. **Middleware Protection**: Authentication is enforced at the middleware level

## Development

When developing locally, make sure to:
1. Set the `API_SECRET_KEY` in your `.env` file
2. Use the same API key in your client applications
3. Check the logs for authentication issues

## Production Deployment

For production:
1. Use a strong, unique API key
2. Store the API key securely (e.g., in environment variables)
3. Rotate the API key regularly
4. Monitor authentication logs for suspicious activity
5. Consider implementing rate limiting for additional security
