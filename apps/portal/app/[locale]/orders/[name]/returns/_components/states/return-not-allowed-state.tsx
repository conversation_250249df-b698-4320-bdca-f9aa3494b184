'use client';

import { ReturnLabelGenerator } from '@/components/return-label-generator';
import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import type { Dictionary } from '@repo/internationalization';
import { AlertCircle, Calendar } from 'lucide-react';
import SelectedItemsDisplay from '../selected-items-display';
import type { FlowState } from '../types';

// Helper functions to get the appropriate messages based on return status
function getReturnStatusTitle(
  status: string | null | undefined,
  dictionary: Dictionary
): string {
  switch (status) {
    case 'rejected':
      return dictionary.return?.already_rejected;
    case 'pending':
      return dictionary.return?.already_pending;
    case 'approved':
      return dictionary.return?.already_approved;
    case 'completed':
      return dictionary.return?.already_completed;
    default:
      return (
        dictionary.return?.already_processed ||
        dictionary.return?.already_processed_summary
      );
  }
}

function getReturnStatusDetails(
  status: string | null | undefined,
  dictionary: Dictionary
): string {
  switch (status) {
    case 'pending':
      return dictionary.return?.pending_details;
    case 'approved':
      return dictionary.return?.approved_details;
    case 'completed':
      return dictionary.return?.completed_details;
    default:
      return dictionary.return?.already_processed_details;
  }
}

interface ReturnNotAllowedStateProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  flowState: FlowState;
}

export default function ReturnNotAllowedState({
  selectedItems,
  order,
  dictionary,
  flowState,
}: ReturnNotAllowedStateProps) {
  // Check if all items have been processed
  const allItemsProcessed =
    flowState.lineItemsProcessed &&
    selectedItems.every((item) =>
      flowState.lineItemsProcessed?.includes(item.node.id)
    );

  return (
    <div className="space-y-6">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      {!allItemsProcessed && flowState.isExpired && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-5 w-5" />
          <AlertDescription className="ml-2">
            <p className="font-medium">
              {dictionary.return.expired_message.replace(
                '{days}',
                String(flowState.autoApprovalDays)
              )}
            </p>
            <p className="mt-1 text-sm">
              {dictionary.return.expired_policy.replace(
                '{days}',
                String(flowState.autoApprovalDays)
              )}
            </p>
          </AlertDescription>
        </Alert>
      )}

      {allItemsProcessed && (
        <>
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="ml-2">
              <p className="font-medium">
                {getReturnStatusTitle(
                  flowState.existingReturnStatus,
                  dictionary
                )}
              </p>
              <p className="mt-1 text-sm">
                {getReturnStatusDetails(
                  flowState.existingReturnStatus,
                  dictionary
                )}
              </p>
            </AlertDescription>
          </Alert>

          {/* Show download label option if approved and print */}
          {flowState.existingReturnStatus === 'approved' &&
            flowState.existingReturnLabelOption === 'print' &&
            flowState.existingReturnNumber && (
              <div className="mb-4">
                <ReturnLabelGenerator
                  orderName={order.name.replace('#', '')}
                  returnNumber={flowState.existingReturnNumber}
                  customerName={
                    order.shippingAddress
                      ? `${order.shippingAddress.firstName} ${order.shippingAddress.lastName}`
                      : undefined
                  }
                  customerAddress={
                    order.shippingAddress
                      ? `${order.shippingAddress.address1}, ${order.shippingAddress.city}, ${order.shippingAddress.province} ${order.shippingAddress.zip}`
                      : undefined
                  }
                  dictionary={dictionary}
                />
              </div>
            )}
        </>
      )}

      {/* Show order date information */}
      {flowState.orderDate && (
        <div className="mt-4 text-muted-foreground text-sm">
          <p className="flex items-center">
            <Calendar className="mr-2 h-4 w-4" />
            {order.fulfillments?.some(
              (fulfillment) =>
                fulfillment.status === 'SUCCESS' ||
                fulfillment.status === 'PENDING' ||
                fulfillment.status === 'OPEN'
            )
              ? dictionary.return?.delivery_date
              : dictionary.return?.order_date}
            {flowState.orderDate.toLocaleDateString()}
          </p>
          {order.fulfillments?.some(
            (fulfillment) =>
              fulfillment.status === 'SUCCESS' ||
              fulfillment.status === 'PENDING' ||
              fulfillment.status === 'OPEN'
          ) && (
            <p className="mt-1 ml-6 text-xs">
              {dictionary.return?.return_period_calculation}
            </p>
          )}
        </div>
      )}

      <div className="mt-4 rounded-lg bg-muted p-4">
        <h4 className="mb-2 font-medium">
          {dictionary.return?.need_assistance}
        </h4>
        <p className="text-sm">{dictionary.return?.assistance_message}</p>
      </div>
    </div>
  );
}
