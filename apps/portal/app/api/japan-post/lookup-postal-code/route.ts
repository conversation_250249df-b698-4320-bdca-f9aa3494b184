import { env } from '@/env';
import {
  formatJapanPostAddress,
  isJapanPostAvailable,
  searchAddressByPostalCode,
} from '@/lib/japan-post';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Check if Japan Post API is available for this environment
    if (!isJapanPostAvailable()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Japan Post API is not available in this region',
          error_ja: 'この地域では日本郵便APIを利用できません。',
        },
        { status: 400 }
      );
    }

    const { postalCode }: { postalCode: string } = await request.json();

    log.info('Japan Post postal code lookup request:', { postalCode });

    if (!postalCode) {
      return NextResponse.json(
        {
          success: false,
          error: 'Postal code is required',
          error_ja: '郵便番号は必須です。',
        },
        { status: 400 }
      );
    }

    // Validate postal code format
    const cleanPostalCode = postalCode.replace(/[-\s]/g, '');
    // biome-ignore lint/performance/useTopLevelRegex: <explanation>
    if (!/^\d{7}$/.test(cleanPostalCode)) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Invalid postal code format. Please enter a 7-digit postal code.',
          error_ja:
            '郵便番号の形式が正しくありません。7桁の数字を入力してください。',
        },
        { status: 400 }
      );
    }

    try {
      // Search for addresses using Japan Post API
      const addresses = await searchAddressByPostalCode(postalCode);

      if (addresses.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'No addresses found for this postal code',
            error_ja: 'この郵便番号に該当する住所が見つかりませんでした。',
            addresses: [],
          },
          { status: 404 }
        );
      }

      // Format addresses for frontend use
      const formattedAddresses = addresses.map(formatJapanPostAddress);

      log.info('Successfully found addresses for postal code:', {
        postalCode,
        count: addresses.length,
      });

      return NextResponse.json(
        {
          success: true,
          addresses: formattedAddresses,
          count: addresses.length,
        },
        { status: 200 }
      );
    } catch (apiError) {
      log.error('Japan Post API error:', {
        error: apiError,
        postalCode,
      });

      // Handle specific API errors
      if (apiError instanceof Error) {
        if (apiError.message.includes('Invalid postal code format')) {
          return NextResponse.json(
            {
              success: false,
              error:
                'Invalid postal code format. Please enter a 7-digit postal code.',
              error_ja:
                '郵便番号の形式が正しくありません。7桁の数字を入力してください。',
            },
            { status: 400 }
          );
        }

        if (apiError.message.includes('Token request failed')) {
          return NextResponse.json(
            {
              success: false,
              error: 'Authentication failed with Japan Post API',
              error_ja: '日本郵便APIの認証に失敗しました。',
            },
            { status: 500 }
          );
        }

        if (apiError.message.includes('Address search failed')) {
          return NextResponse.json(
            {
              success: false,
              error: 'Failed to search addresses. Please try again.',
              error_ja: '住所の検索に失敗しました。もう一度お試しください。',
            },
            { status: 500 }
          );
        }
      }

      // Generic error response
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to lookup postal code. Please try again.',
          error_ja: '郵便番号の検索に失敗しました。もう一度お試しください。',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    log.error('Error in Japan Post postal code lookup:', { error });
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred. Please try again.',
        error_ja: '予期しないエラーが発生しました。もう一度お試しください。',
      },
      { status: 500 }
    );
  }
}

// Handle GET requests for API documentation or health check
export function GET() {
  return NextResponse.json(
    {
      name: 'Japan Post Postal Code Lookup API',
      name_ja: '日本郵便 郵便番号検索 API',
      description: 'Search for Japanese addresses by postal code',
      description_ja: '郵便番号から日本の住所を検索します',
      available: isJapanPostAvailable(),
      region: env.NEXT_PUBLIC_REGION,
      usage: {
        method: 'POST',
        body: {
          postalCode: 'string (7 digits, e.g., "1000001" or "100-0001")',
        },
        response: {
          success: 'boolean',
          addresses: 'array of formatted address objects',
          count: 'number of addresses found',
        },
      },
      usage_ja: {
        method: 'POST',
        body: {
          postalCode: '文字列（7桁。例: "1000001" または "100-0001"）',
        },
        response: {
          success: '真偽値',
          addresses: '整形済み住所オブジェクトの配列',
          count: '見つかった住所の件数',
        },
      },
    },
    { status: 200 }
  );
}
