import { getOrderByName } from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName, email }: { orderName: string; email: string } =
      await request.json();

    log.info(`Checking fulfillment status for order ${orderName}`);

    if (!orderName || !email) {
      return NextResponse.json(
        { error: 'Order name and email are required' },
        { status: 400 }
      );
    }

    // Verify the order with email
    const order = await getOrderByName(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
          key: 'order_not_found_full',
        },
        { status: 200 }
      );
    }

    const isFulfilled = order.fulfillments.some(
      (fulfillment) => fulfillment.status === 'SUCCESS'
    );

    return NextResponse.json(
      {
        success: true,
        isFulfilled,
        message: isFulfilled
          ? 'Order has been fulfilled'
          : 'Order has not been fulfilled yet',
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error checking fulfillment status:', { error });
    return NextResponse.json(
      { error: 'An error occurred while checking the fulfillment status' },
      { status: 500 }
    );
  }
}
