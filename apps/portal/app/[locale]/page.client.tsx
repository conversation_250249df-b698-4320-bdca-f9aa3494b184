'use client';

import { getLocale } from '@/lib/utils';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { AlertCircle } from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface OrderLookupFormProps {
  dictionary: Dictionary;
}

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

export default function OrderLookupForm({ dictionary }: OrderLookupFormProps) {
  const [orderNumber, setOrderNumber] = useState('');
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // Check for error parameters in URL when component mounts
  useEffect(() => {
    const errorType = searchParams?.get('error');
    const orderNameFromParams = searchParams?.get('orderName');

    if (errorType === 'verification_failed' && orderNameFromParams) {
      setOrderNumber(orderNameFromParams);
      setError(
        dictionary.message.verification_failed ||
          'Email verification failed. Please enter the email address associated with this order.'
      );
    }
  }, [searchParams, dictionary]);

  const handleFindOrder = async () => {
    // Reset any previous error
    setError('');

    // Check if both fields are filled
    if (!orderNumber.trim()) {
      setError(`${dictionary.form.order} ${dictionary.form.required}`);
      return;
    }

    if (!email.trim()) {
      setError(`${dictionary.form.email} ${dictionary.form.required}`);
      return;
    }

    // Basic email validation
    if (!emailRegex.test(email.trim())) {
      setError(dictionary.form.email_invalid);
      return;
    }

    try {
      setIsLoading(true);

      // Call the API to verify the order with email
      const response = await fetch('/api/verify-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderName: orderNumber.trim(),
          email: email.trim(),
        }),
      });

      const data: {
        verified: boolean;
        message?: string;
        key?: string;
        orderName?: string;
      } = await response.json();

      if (!response.ok) {
        setError(
          data.key
            ? dictionary.message[data.key]
            : data.message || dictionary.message.error
        );
        setIsLoading(false);
        return;
      }

      if (!data.verified) {
        setError(
          data.key
            ? dictionary.message[data.key]
            : data.message || dictionary.message.order_not_found
        );
        setIsLoading(false);
        return;
      }

      if (!pathname) {
        router.push(
          `/orders/${orderNumber.trim()}/services?email=${encodeURIComponent(email.trim())}`
        );

        return;
      }

      // Get the locale from the pathname
      const locale = getLocale(pathname);

      // If verification is successful, navigate to the service selection page with the email parameter
      router.push(
        `/${locale}/orders/${orderNumber.trim()}/services?email=${encodeURIComponent(email.trim())}`
      );
    } catch (err) {
      log.error('Error verifying order:', { err });
      setError(dictionary.message.unexpected_error);
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.form.find_order}</CardTitle>
        <CardDescription>{dictionary.form.enter_details}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Input
            type="text"
            placeholder={dictionary.form.order_placeholder}
            value={orderNumber}
            onChange={(e) => setOrderNumber(e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Input
            type="email"
            placeholder={dictionary.form.email_placeholder}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleFindOrder()} // Optional: allow Enter key
          />
        </div>
        {error && (
          <Alert variant="destructive" className="py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <Button
          onClick={handleFindOrder}
          className="w-full"
          disabled={isLoading}
        >
          {isLoading
            ? dictionary.message.verifying
            : dictionary.form.find_order_button}
        </Button>
      </CardContent>
      <CardFooter className="text-muted-foreground text-sm">
        {dictionary.form.help_text}
      </CardFooter>
    </Card>
  );
}
