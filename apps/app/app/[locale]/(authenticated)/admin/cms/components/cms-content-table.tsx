'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import { useDebounce } from '@repo/design-system/hooks/use-debounce';
import type { Dictionary } from '@repo/internationalization';
import {
  type ColumnDef,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  type SerializedCmsContent,
  deleteCmsContent,
  getCmsContents,
} from '../actions';
import { columns as columnsDef } from './columns';
import { CreateCmsDialog } from './create-cms-dialog';

interface CmsContentTableProps {
  columns: ColumnDef<SerializedCmsContent, unknown>[];
  initialData: SerializedCmsContent[];
  dictionary: Dictionary;
}

export function CmsContentTable({
  columns: _columns,
  initialData,
  dictionary,
}: CmsContentTableProps) {
  const router = useRouter();
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 500);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [data, setData] = useState<SerializedCmsContent[]>(initialData);
  const [loading, setLoading] = useState(false);

  const handleDelete = async (id: string) => {
    try {
      await deleteCmsContent(id);
      setData((prev) => prev.filter((item) => item.id !== id));
      toast.success(dictionary.admin.common.delete_success);
    } catch (_error) {
      toast.error(dictionary.admin.common.delete_error);
    }
  };

  // Fetch data when search changes
  useEffect(() => {
    const fetchData = async () => {
      if (debouncedSearch) {
        setLoading(true);
        try {
          const contents = await getCmsContents({
            searchQuery: debouncedSearch,
          });
          setData(contents);
        } catch (error) {
          console.error('Error fetching CMS contents:', error);
        } finally {
          setLoading(false);
        }
      } else {
        setData(initialData);
      }
    };

    fetchData();
  }, [debouncedSearch, initialData]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const table = useReactTable({
    data,
    columns: columnsDef(dictionary, handleDelete),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  return (
    <>
      <div className="flex items-center justify-between gap-4 py-4">
        <div className="relative max-w-sm flex-1">
          <Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search content..."
            value={search}
            onChange={(event) => setSearch(event.target.value)}
            className="pl-8"
          />
        </div>
        <CreateCmsDialog dictionary={dictionary} />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => router.push(`/admin/cms/${row.original.id}`)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columnsDef.length}
                  className="h-24 text-center"
                >
                  {loading ? 'Loading...' : 'No results found.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>
    </>
  );
}
