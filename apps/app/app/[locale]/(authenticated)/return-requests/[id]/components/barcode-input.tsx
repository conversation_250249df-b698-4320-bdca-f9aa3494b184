'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

import type { Dictionary } from '@repo/internationalization';

interface BarcodeInputProps {
  exchangeType: string | null;
  exchangeStep?: 'outgoing' | 'incoming';
  scanningStep?: 'package' | 'items';
  returnNumber: string;
  returnLabelNumber?: string;
  onScan: (value: string) => void;
  disabled?: boolean;
  dictionary: Dictionary;
}

export function BarcodeInput({
  exchangeType,
  exchangeStep,
  scanningStep,
  returnNumber,
  returnLabelNumber,
  onScan,
  disabled = false,
  dictionary,
}: BarcodeInputProps) {
  const [barcode, setBarcode] = useState('');
  const [delayDebounce, setDelayDebounce] = useState<NodeJS.Timeout>();
  const barcodeInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus the barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current && !disabled) {
      barcodeInputRef.current.focus();
    }
  }, [disabled]);

  // Keep focus on barcode input after scanning
  useEffect(() => {
    if (barcodeInputRef.current && !barcode && !disabled) {
      barcodeInputRef.current.focus();
    }
  }, [barcode, disabled]);

  const handleInput = useCallback(
    (value: string) => {
      clearTimeout(delayDebounce);
      if (value.trim()) {
        const delayDebounceTimeout = setTimeout(() => {
          onScan(value.trim());
          setBarcode('');
          // Refocus the input after processing
          if (barcodeInputRef.current) {
            barcodeInputRef.current.focus();
          }
        }, 300);
        setDelayDebounce(delayDebounceTimeout);
      }
    },
    [delayDebounce, onScan]
  );

  const handleBarcodeSubmit = () => {
    if (!barcode.trim()) {
      return;
    }
    clearTimeout(delayDebounce);
    onScan(barcode.trim());
    setBarcode('');
    // Refocus the input after manual submission
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleBarcodeSubmit();
    }
  };

  const getInputLabel = (): string => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.package_verification;
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return dictionary.admin.scanner.barcode_scanner.outgoing_items;
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return dictionary.admin.scanner.barcode_scanner.incoming_items;
    }
    return dictionary.admin.scanner.barcode_scanner.item_verification;
  };

  const getPlaceholder = (): string => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.scan_package.replace(
        '{returnNumber}',
        returnLabelNumber
          ? `${returnNumber} / ${returnLabelNumber}`
          : returnNumber
      );
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return dictionary.admin.scanner.barcode_scanner.scan_outgoing
        .replace('{scannedCount}', '0')
        .replace('{totalCount}', '0')
        .split('(')[0]
        .trim();
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return dictionary.admin.scanner.barcode_scanner.scan_incoming
        .replace('{scannedCount}', '0')
        .replace('{totalCount}', '0')
        .split('(')[0]
        .trim();
    }
    return dictionary.admin.scanner.barcode_scanner.placeholder;
  };

  const getHelpText = (): string => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.scan_package.replace(
        '{returnNumber}',
        returnLabelNumber
          ? `${returnNumber} / ${returnLabelNumber}`
          : returnNumber
      );
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return dictionary.admin.scanner.barcode_scanner.scan_outgoing
        .replace('{scannedCount}', '0')
        .replace('{totalCount}', '0');
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return dictionary.admin.scanner.barcode_scanner.scan_incoming
        .replace('{scannedCount}', '0')
        .replace('{totalCount}', '0');
    }
    return dictionary.admin.scanner.barcode_scanner.placeholder;
  };

  const shouldShowInput =
    exchangeType !== 'exchange' ||
    (exchangeType === 'exchange' &&
      (exchangeStep === 'outgoing' || exchangeStep === 'incoming'));

  if (!shouldShowInput) {
    return null;
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="barcode-input" className="font-medium text-sm">
        {getInputLabel()}
      </Label>
      <div className="flex gap-2">
        <Input
          ref={barcodeInputRef}
          id="barcode-input"
          type="text"
          value={barcode}
          onChange={(e) => {
            setBarcode(e.target.value);
            handleInput(e.target.value);
          }}
          placeholder={getPlaceholder()}
          className="flex-1 rounded-md border border-gray-300 px-3 py-2 font-mono"
          onKeyDown={handleKeyDown}
          autoComplete="off"
          disabled={disabled}
          autoFocus
        />
        <Button
          onClick={handleBarcodeSubmit}
          disabled={!barcode.trim() || disabled}
          variant="outline"
        >
          {dictionary.admin.common.submit}
        </Button>
      </div>
      <p className="text-gray-500 text-sm">{getHelpText()}</p>
    </div>
  );
}
