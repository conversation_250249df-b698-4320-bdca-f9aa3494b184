import {
  sendAdminAddressChangeNotificationEmail,
  sendAdminRequestNotificationEmail,
  sendExchangeItemReceivedEmail,
  sendExchangeItemShippedEmail,
  sendExchangeRequestAutoApprovedEmail,
  sendExchangeRequestManualApprovedEmail,
  sendExchangeRequestManualReceivedEmail,
  sendRequestDeclinedEmail,
  sendReturnRequestAutoApprovedEmail,
  sendReturnRequestManualApprovedEmail,
  sendReturnRequestManualReceivedEmail,
  sendSendersConfirmationEmail,
  sendSendersShareEmail,
} from '@repo/email';
import { log } from '@repo/observability/log';
import { type Job, Worker } from 'bullmq';
import { getRedisConnection } from './redis';
import type { EmailJobData, EmailJobResult } from './types';

// Email sending function mapping
const emailSenders = {
  'return-request-auto-approved': sendReturnRequestAutoApprovedEmail,
  'return-request-manual-received': sendReturnRequestManualReceivedEmail,
  'return-request-manual-approved': sendReturnRequestManualApprovedEmail,
  'exchange-request-auto-approved': sendExchangeRequestAutoApprovedEmail,
  'exchange-item-shipped': sendExchangeItemShippedEmail,
  'exchange-item-received': sendExchangeItemReceivedEmail,
  'exchange-request-manual-received': sendExchangeRequestManualReceivedEmail,
  'exchange-request-manual-approved': sendExchangeRequestManualApprovedEmail,
  'request-declined': sendRequestDeclinedEmail,
  'admin-address-change-notification': sendAdminAddressChangeNotificationEmail,
  'admin-request-notification': sendAdminRequestNotificationEmail,
  'senders-share': sendSendersShareEmail,
  'senders-confirmation': sendSendersConfirmationEmail,
} as const;

// Process email job
const processEmailJob = async (
  job: Job<EmailJobData>
): Promise<EmailJobResult> => {
  const { data } = job;
  const startTime = Date.now();

  try {
    log.info('Processing email job', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      returnNumber: data.returnNumber,
      attempt: job.attemptsMade + 1,
    });

    // Get the appropriate email sender function
    const emailSender = emailSenders[data.type];
    if (!emailSender) {
      throw new Error(`Unknown email type: ${data.type}`);
    }

    // Prepare return label data for PDF generation
    const returnLabelData =
      data.returnLabelOption && data.orderName
        ? {
            returnLabelOption: data.returnLabelOption,
            orderName: data.orderName,
            customerName: data.customerName,
            customerAddress: data.customerAddress,
            companyName: data.companyName,
            companyAddress: data.companyAddress,
            returnDepartment: data.returnDepartment,
          }
        : undefined;

    // Prepare arguments based on email type
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    let result: any;

    if (
      !data.returnNumber &&
      data.type !== 'senders-share' &&
      data.type !== 'senders-confirmation'
    ) {
      throw new Error(
        'Return number is required for all email types except senders-share and senders-confirmation'
      );
    }

    const returnNumber = data.returnNumber ?? '';

    switch (data.type) {
      case 'return-request-auto-approved':
        result = await (
          emailSender as typeof sendReturnRequestAutoApprovedEmail
        )(data.to, returnNumber, data.refundDays, returnLabelData);
        break;
      case 'return-request-manual-received':
        result = await (
          emailSender as typeof sendReturnRequestManualReceivedEmail
        )(data.to, returnNumber, data.reviewDays ?? '3');
        break;
      case 'return-request-manual-approved':
        result = await (
          emailSender as typeof sendReturnRequestManualApprovedEmail
        )(
          data.to,
          returnNumber,
          data.refundDays,
          data.returnInstructions,
          returnLabelData
        );
        break;
      case 'exchange-item-shipped': {
        if (!data.trackingNumber) {
          throw new Error(
            'Tracking number is required for exchange-item-shipped emails'
          );
        }

        result = await (emailSender as typeof sendExchangeItemShippedEmail)(
          data.to,
          returnNumber,
          data.trackingNumber
        );

        break;
      }
      case 'exchange-request-auto-approved':
        result = await (
          emailSender as typeof sendExchangeRequestAutoApprovedEmail
        )(data.to, returnNumber, returnLabelData);
        break;
      case 'exchange-item-received':
        result = await (emailSender as typeof sendExchangeItemReceivedEmail)(
          data.to,
          returnNumber
        );
        break;
      case 'exchange-request-manual-received':
        result = await (
          emailSender as typeof sendExchangeRequestManualReceivedEmail
        )(data.to, returnNumber);
        break;
      case 'exchange-request-manual-approved':
        result = await (
          emailSender as typeof sendExchangeRequestManualApprovedEmail
        )(data.to, returnNumber, returnLabelData);
        break;
      case 'request-declined':
        result = await (emailSender as typeof sendRequestDeclinedEmail)(
          data.to,
          returnNumber
        );
        break;
      case 'admin-address-change-notification': {
        if (
          !data.customerInfo ||
          !data.originalAddress ||
          !data.newAddress ||
          !data.orderName
        ) {
          throw new Error(
            'Customer info, original address, new address, and order name are required for admin address change notification emails'
          );
        }

        result = await (
          emailSender as typeof sendAdminAddressChangeNotificationEmail
        )(
          data.to,
          data.orderName,
          data.customerInfo,
          data.originalAddress,
          data.newAddress
        );
        break;
      }
      case 'admin-request-notification': {
        if (
          !data.customerInfo ||
          !data.requestType ||
          !data.requestDetails ||
          !data.orderName
        ) {
          throw new Error(
            'Customer info, request type, request details, and order name are required for admin request notification emails'
          );
        }

        result = await (
          emailSender as typeof sendAdminRequestNotificationEmail
        )(
          data.to,
          data.orderName,
          data.customerInfo,
          data.requestType,
          data.requestDetails,
          data.returnItems ?? []
        );

        break;
      }
      case 'senders-share': {
        if (
          !data.shareId ||
          !data.senderEmail ||
          !data.fileCount ||
          !data.expiresAt
        ) {
          log.info('Missing required data for senders share email', {
            data,
          });

          throw new Error(
            'Share ID, sender email, title, message, file count, expires at, and is private are required for senders share emails'
          );
        }

        result = await (emailSender as typeof sendSendersShareEmail)(
          data.to,
          data.shareId,
          data.senderEmail,
          data.title ?? '',
          data.message ?? '',
          data.fileCount,
          data.expiresAt,
          data.isPrivate ?? false
        );
        break;
      }
      case 'senders-confirmation': {
        if (!data.shareId || !data.fileCount || !data.expiresAt) {
          log.info('Missing required data for senders confirmation email', {
            data,
          });

          throw new Error(
            'Share ID, title, message, file count, expires at, and is private are required for senders confirmation emails'
          );
        }

        result = await (emailSender as typeof sendSendersConfirmationEmail)(
          data.to,
          data.shareId,
          data.title ?? '',
          data.message ?? '',
          data.fileCount,
          data.expiresAt,
          data.isPrivate ?? false
        );
        break;
      }
      default:
        throw new Error(`Unhandled email type: ${data.type}`);
    }

    if (!result.success) {
      throw new Error(result.error?.toString() || 'Email sending failed');
    }

    const processingTime = Date.now() - startTime;
    const jobResult: EmailJobResult = {
      success: true,
      messageId: result.result?.messageId || 'unknown',
      sentAt: new Date().toISOString(),
      attempts: job.attemptsMade + 1,
    };

    log.info('Email job completed successfully', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      processingTime,
      attempts: job.attemptsMade + 1,
      data,
    });

    return jobResult;
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';

    log.error('Email job failed', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      error: errorMessage,
      processingTime,
      attempts: job.attemptsMade + 1,
    });

    throw error;
  }
};

// Create and start the worker
export const createEmailWorker = (concurrency = 5) => {
  const worker = new Worker('email-queue', processEmailJob, {
    connection: getRedisConnection(),
    concurrency,
    // removeOnComplete: 100,
    // removeOnFail: 50,
  });

  // Worker event listeners
  worker.on('ready', () => {
    log.info('Email worker is ready', { concurrency });
  });

  worker.on('error', (error) => {
    log.error('Email worker error', { error });
  });

  worker.on('failed', (job, error) => {
    log.error('Email job failed in worker', {
      jobId: job?.id,
      error: error.message,
      attempts: job?.attemptsMade,
    });
  });

  worker.on('completed', (job, result) => {
    log.info('Email job completed in worker', {
      jobId: job.id,
      result,
    });
  });

  return worker;
};

// Graceful shutdown
export const shutdownWorker = async (worker: Worker) => {
  log.info('Shutting down email worker...');
  await worker.close();
  log.info('Email worker shut down successfully');
};
